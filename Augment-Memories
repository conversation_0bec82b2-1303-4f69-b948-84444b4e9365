
# Fibonacci Levels and Analysis
- Fibonacci analyzer calculates on daily timeframe using first two of last three swing points, with golden spot 1 (0.34-0.382), golden spot 2 (0.618-0.66) and OTE (0.705-0.782) scored as zones while other levels scored individually.
- Add negative (-) Fibonacci levels and OTE (Optimal Trade Entry) level at 0.705 in calculations.
- Add Fibonacci zone groups: (0.114-0.214), (0.295-0.214), (0.786-0.886), (1.114-1.214) and (-0.114 to -0.214).
- Fibonacci retracement should be calculated from HH to HL for uptrends or from LL to LH for downtrends.
- Fibonacci level tolerance value should be set to 0.5% instead of 1.0% with a 'price-based' approach for tolerance calculations.
- When Fibonacci levels are detected, log at INFO level for all levels (both negative and positive) without excessive logging.
- When price is far from Fibonacci levels or zones, the system should use the default entry level (last price) instead.
- FVG (Fair Value Gap) and Fibonacci are separate concepts; 'daily FVG EQ' refers to the equilibrium level of a Fair Value Gap on the daily timeframe.

# Scoring System
- The scoring system uses 4-hour (240) timeframe for base scoring, with other timeframes for confirmation scoring.
- Scoring should be based on pattern detection, with no scoring performed if patterns aren't detected.
- Patterns alone should provide enough score for symbols to be included in rankings.
- Only use daily Fibonacci levels for confirmation scoring, not 4H timeframe Fibonacci calculations.
- Fibonacci levels 0 and 1 should not be scored in the scoring system.
- Add a minimum score threshold of 2.0 to filter out low-scoring symbols.
- Format the Net Score section in reports as '📊 Net Score: X.X |' with the pipe symbol at the end.
- Remove point values from Base Signals section in reports.
- Pattern scoring for different timeframes: D (daily) = 2.0 points and 12h = 0.75 point.
- SFP analyzer should only list the last two SFPs for each symbol and direction with pivot length (plen) parameter set to 10.
- Remove divergence scoring from 720 (12-hour) and D (daily) timeframes.
- Format pattern displays with 'SHORT' instead of 'BEAR' and 'LONG' instead of 'BULL' in the scoring reports.
- User prefers to keep Confirmation and Negative details visible in reports.

# Premium/Discount Zones
- Premium zone is when price > eq (bearish 4h OB/FVG scoring), discount zone is when price < eq (bullish 4h OB/FVG scoring).
- Fibonacci levels (0, 1, EQ) for Premium/Discount calculation should be taken from daily (D) timeframe.
- Apply negative scoring when patterns conflict with zones: bearish pattern in discount zone or bullish pattern in premium zone should receive -0.5 points.
- Premium/Discount status logs should list whether each symbol is in premium or discount zone, and list FVGs and OBs in these zones.

# Smart Entry Strategy
- The 0.5 (EQ) level should be considered as a valid entry point if it's closer than golden spots.
- When determining entry prices in the smart_entry module, the Fibonacci proximity threshold should be 1% instead of 5%.
- Entry strategy should consider all Fibonacci levels including negative values like -0.618 and zones like Extend Zone 1 (0.114-0.214).
- Smart entry strategy should use previous pivot points with +1% liquidity buffer for stop loss calculation (except for TRIT/TRIB patterns which use 2.5%), and 4h Fibonacci levels (0.114-0.886) for entry points instead of daily timeframe.
- Stop loss level should be set to 2.5% by default with a maximum threshold of 3%, ignoring pivot points or Fibonacci levels that fall within the default range.
- Stop loss levels should use pivot points (HH/LL) when available instead of fixed percentage (2.33%).

# Telegram Reporting
- Generate transaction statistics reports every 12 hours instead of every 4 hours.
- After sending a report, the message should remain unchanged until the symbol's pattern condition changes.
- Format pattern displays with arrow emoji and shortened format (e.g., '⬆️ D PHL3' instead of 'D BULLISH Pattern (PHL3)').
- When a symbol changes pattern direction, send a new Telegram report and consider the previous trade closed.
- Organize trade reports with specific formatting: Net Score on first line, followed by Base/Conf/Neg scores with indentation, then Trade Levels with Fib indicator, and Base Signals at the bottom.
- Display 'Fib' in trade levels section only when Fibonacci levels are used for entry determination.
- When a new pivot forms with a pattern after a report has been sent to Telegram, the system should recognize this as a new signal and send a new report.

# Divergence Analysis
- Develop a divergence verification mechanism to check all points between pivot points to reduce false positives.
- Explore alternatives to 'Divergence for Many Indicators v4' for better accuracy in divergence detection.

# Deployment & Logging
- User abandoned Render.com deployment and restored logging capabilities by reverting main.py and notification_service.py from backup.
- User doesn't want log and statistics tracking in the Render deployment.
- Even when general logging is disabled, scoring_combined logs should be retained for at least 48 hours to prevent duplicate Telegram notifications.
- On render.com free tier, prefer storing small files like logs in memory rather than disk since memory remains active while the program is running.