"""
İstatistik Takip Modülü (StatsTracker)
-------------------------------------
<PERSON><PERSON>, Automaton tarafından üretilen sinyallerin takibi,
i<PERSON><PERSON> sonuçlarının kaydedilmesi ve performans istatistiklerinin tutulmasından sorumludur.

Özellikler:
- <PERSON><PERSON> kaydı: Üretilen her sinyal benzersiz ID ile kaydedilir
- <PERSON>yal takibi: Aktif sinyallerin durumu periyodik olarak kontrol edilir
- Sonuç analizi: Tamamlanan işlemlerin sonuçları kaydedilir ve istatistik üretilir
- Performans raporu: Başarı oranı, ortalama kar/zarar, en başarılı göstergeler gibi metrikler hesaplanır
"""

import os
import csv
import json
import time
import shutil
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger

# CSV alan isimleri
SIGNAL_FIELDS = [
    "signal_id", "symbol", "timeframe", "entry_time", "entry_price", "direction",
    "score", "sl_price", "tp_price", "tp1_price", "tp2_price", "tp3_price", "status", "confirmations",
    "result_time", "result_price", "profit_percentage", "volatility_level", "fib_level"
]

class StatsTracker:
    """
    İşlem sinyallerini takip eder ve istatistik üretir.

    Özellikler:
        - Sinyalleri kalıcı CSV dosyalarında saklar
        - Aktif sinyalleri takip eder
        - Tamamlanan işlemlerin sonuçlarını kaydeder
        - Performans metrikleri hesaplar
    """

    def __init__(self, stats_dir: str = "stats", main_timeframe: str = "240"):
        """
        StatsTracker sınıfını başlatır.

        Args:
            stats_dir: İstatistik dosyalarının saklanacağı dizin
            main_timeframe: İstatistiklerin tutulacağı ana zaman dilimi (varsayılan: "240" = 4h)
        """
        self.stats_dir = stats_dir
        self.main_timeframe = main_timeframe

        # Dizin yapısını oluştur
        os.makedirs(stats_dir, exist_ok=True)

        # Yedekler için dizin oluştur
        self.backup_dir = os.path.join(stats_dir, "backups")
        os.makedirs(self.backup_dir, exist_ok=True)

        # Dosya yolları
        self.signals_file = os.path.join(stats_dir, "trade_signals.csv")
        self.results_file = os.path.join(stats_dir, "trade_results.csv")
        self.metrics_file = os.path.join(stats_dir, "performance_metrics.json")

        # CSV Dosyalarını kontrol et ve migrate et
        self._check_and_migrate_csv()

        # Dosyaları oluştur (yoksa)
        self._initialize_files()

        # Dosyaları yedekle (başlangıç)
        self._backup_stats_files()

        # Aktif sinyaller (hafızada)
        self.active_signals: Dict[str, Dict[str, Any]] = {}

        # En son kontrol zamanı
        self.last_check_time = datetime.now()

        logger.info(f"StatsTracker başlatıldı. Dosya dizini: {stats_dir}, Ana timeframe: {self.main_timeframe}")

        # Başlangıçta aktif sinyalleri yükle
        self._load_active_signals()

    def _check_and_migrate_csv(self):
        """
        CSV dosyalarını kontrol eder ve gerekirse yeni yapıya geçirir
        Timeframe sütunu eksi dosyalarda yoksa eklenir
        """
        if os.path.exists(self.signals_file):
            try:
                # Dosyayı oku
                signals_df = pd.read_csv(self.signals_file)

                # Timeframe sütunu var mı kontrol et
                if 'timeframe' not in signals_df.columns:
                    logger.info(f"CSV yapısı güncelleniyor: timeframe sütunu ekleniyor")

                    # Önce yedek al
                    backup_file = os.path.join(self.backup_dir, f"trade_signals_before_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
                    shutil.copy2(self.signals_file, backup_file)
                    logger.info(f"CSV dosyası yedeklendi: {backup_file}")

                    # Timeframe sütunu ekle (tümü "240" olarak)
                    signals_df['timeframe'] = self.main_timeframe

                    # CSV'yi tekrar yaz
                    signals_df.to_csv(self.signals_file, index=False)
                    logger.info(f"CSV dosyası başarıyla güncellendi")

                # Aynı işlemi results dosyası için de yap
                if os.path.exists(self.results_file):
                    results_df = pd.read_csv(self.results_file)
                    if 'timeframe' not in results_df.columns:
                        # Önce yedek al
                        backup_file = os.path.join(self.backup_dir, f"trade_results_before_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
                        shutil.copy2(self.results_file, backup_file)

                        # Timeframe sütunu ekle
                        results_df['timeframe'] = self.main_timeframe

                        # CSV'yi tekrar yaz
                        results_df.to_csv(self.results_file, index=False)
                        logger.info(f"Results CSV dosyası başarıyla güncellendi")

            except Exception as e:
                logger.error(f"CSV dosyalarını kontrol ederken hata: {e}")

    def _backup_stats_files(self):
        """
        Tüm istatistik dosyalarını yedekler
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            for file_path, name in [
                (self.signals_file, "trade_signals"),
                (self.results_file, "trade_results"),
                (self.metrics_file, "performance_metrics")
            ]:
                if os.path.exists(file_path):
                    backup_file = os.path.join(self.backup_dir, f"{name}_{timestamp}.bak")
                    shutil.copy2(file_path, backup_file)
                    logger.debug(f"{name} dosyası yedeklendi: {backup_file}")

            logger.info(f"Tüm istatistik dosyaları yedeklendi ({timestamp})")
        except Exception as e:
            logger.error(f"Dosyaları yedeklerken hata: {e}")

    def _initialize_files(self):
        """CSV ve JSON dosyalarını oluşturur (yoksa)"""
        # Sinyal dosyası
        if not os.path.exists(self.signals_file):
            with open(self.signals_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS)
                writer.writeheader()

        # Sonuç dosyası
        if not os.path.exists(self.results_file):
            with open(self.results_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS)
                writer.writeheader()

        # Metrik dosyası
        if not os.path.exists(self.metrics_file):
            metrics = {
                "total_signals": 0,
                "completed_trades": 0,
                "successful_trades": 0,
                "failed_trades": 0,
                "success_rate": 0.0,
                "avg_profit_percentage": 0.0,
                "total_profit_percentage": 0.0,
                "tp1_hits": 0,
                "tp2_hits": 0,
                "tp3_hits": 0,
                "tp1_rate": 0.0,
                "tp2_rate": 0.0,
                "tp3_rate": 0.0,
                "best_trade": None,
                "worst_trade": None,
                "best_symbol": None,
                "best_confirmation": None,
                "last_updated": datetime.now().isoformat()
            }
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, indent=4)

    def _validate_signal_data(self, signal_data: Dict[str, Any]) -> bool:
        """
        Sinyal verilerini doğrular

        Args:
            signal_data: Kontrol edilecek sinyal verisi

        Returns:
            bool: Veri doğruysa True, değilse False
        """
        # Gerekli alanlar
        required_fields = ["symbol", "entry_price", "direction", "timeframe"]

        # Tüm gerekli alanlar var mı kontrol et
        if not all(field in signal_data for field in required_fields):
            missing = [field for field in required_fields if field not in signal_data]
            logger.warning(f"Sinyal validasyon hatası: Eksik alanlar {missing}")
            return False

        # Değer kontrolleri
        if not signal_data.get("symbol"):
            logger.warning("Sinyal validasyon hatası: Sembol boş")
            return False

        if signal_data.get("entry_price") is None:
            logger.warning("Sinyal validasyon hatası: Giriş fiyatı tanımlanmamış")
            return False

        if signal_data.get("direction") not in ["BULL", "BEAR"]:
            logger.warning(f"Sinyal validasyon hatası: Geçersiz yön '{signal_data.get('direction')}'")
            return False

        return True

    def _load_active_signals(self):
        """CSV'den aktif sinyalleri yükler"""
        try:
            signals_df = pd.read_csv(self.signals_file)
            active_signals = signals_df[signals_df['status'] == 'ACTIVE']

            # Aktif sinyal sayısını logla
            total_active_count = len(active_signals)
            logging.info(f"CSV'den toplam {total_active_count} aktif sinyal bulundu.")

            for _, signal in active_signals.iterrows():
                signal_data = signal.to_dict()

                # Veri bütünlüğünü kontrol et
                if not self._validate_signal_data(signal_data):
                    logging.warning(f"Geçersiz sinyal verisi: {signal_data}")
                    continue

                signal_id = signal_data.get('signal_id')
                if signal_id:
                    self.active_signals[signal_id] = signal_data
                else:
                    logging.warning(f"Sinyal ID eksik: {signal_data}")

            # *** _check_signals_validity() metodu eksik olduğu için kaldırıldı ***
            # Ana timeframe filtreli ve benzersiz sinyal sayısını logla
            filtered_unique = self._get_unique_latest_signals([
                signal for signal in self.active_signals.values()
                if signal.get("timeframe") == self.main_timeframe
            ])

            logging.info(f"{len(self.active_signals)} aktif sinyal yüklendi. ")
            logging.info(f"Ana timeframe ({self.main_timeframe}) için benzersiz sinyal sayısı: {len(filtered_unique)}")
        except Exception as e:
            logging.error(f"Aktif sinyaller yüklenirken hata: {e}")
            raise

    def generate_signal_id(self, symbol: str, direction: str) -> str:
        """Benzersiz sinyal ID'si oluşturur"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{symbol}_{timestamp}_{direction}"

    def record_signal(self, score_data: Dict[str, Any]) -> str:
        """
        Yeni bir sinyal kaydeder

        Args:
            score_data: ScoringSystem'den gelen puan verisi

        Returns:
            str: Oluşturulan sinyal ID'si
        """
        symbol = score_data.get("symbol")
        net_score = score_data.get("net_score", 0)
        entry_price = score_data.get("entry_price")
        sl_price = score_data.get("stop_loss_price")
        timeframe = score_data.get("base_timeframe")

        # Smart entry seviyelerini al
        entry_levels = score_data.get("entry_levels", {})

        # TP1, TP2, TP3 fiyatlarını smart_entry'den al
        tp1_price = entry_levels.get("tp1", score_data.get("tp1_price", score_data.get("take_profit_price")))
        tp2_price = entry_levels.get("tp2", score_data.get("tp2_price"))
        tp3_price = entry_levels.get("tp3", score_data.get("tp3_price"))

        # Stop loss'u da smart_entry'den al (eğer varsa)
        if entry_levels.get("stop_loss") and not sl_price:
            sl_price = entry_levels.get("stop_loss")

        # Entry price'ı da smart_entry'den al (eğer varsa)
        if entry_levels.get("primary_entry") and not entry_price:
            entry_price = entry_levels.get("primary_entry")

        # Volatilite seviyesi ve Fibonacci seviyesini al
        volatility_level = entry_levels.get("volatility_level", score_data.get("volatility_level", "unknown"))
        fib_level = entry_levels.get("fib_level", score_data.get("fib_level"))

        # Sadece ana timeframe sinyallerini kaydet
        if timeframe != self.main_timeframe:
            logger.debug(f"Timeframe {timeframe} sinyali ana timeframe {self.main_timeframe} olmadığı için kaydedilmedi: {symbol}")
            return ""

        # Yönü belirle
        direction = "UNKNOWN"
        for detail in score_data.get("base_details", []):
            if isinstance(detail, tuple) and len(detail) > 0:
                if "Bullish" in detail[0]:
                    direction = "BULL"
                    break
                elif "Bearish" in detail[0]:
                    direction = "BEAR"
                    break

        # Teyit detaylarını listeye dönüştür
        confirmation_details = []
        for detail in score_data.get("confirmation_details", []):
            if ": +" in detail:
                parts = detail.split(": +", 1)
                confirmation_details.append({
                    "name": parts[0],
                    "score": float(parts[1]) if len(parts) > 1 else 1.0
                })

        # Sinyal ID'si oluştur
        signal_id = self.generate_signal_id(symbol, direction)

        # Sinyal verisi oluştur
        signal_data = {
            "signal_id": signal_id,
            "symbol": symbol,
            "timeframe": timeframe,
            "entry_time": datetime.now().isoformat(),
            "entry_price": entry_price,
            "direction": direction,
            "score": net_score,
            "sl_price": sl_price,
            "tp1_price": tp1_price,
            "tp2_price": tp2_price,
            "tp3_price": tp3_price,
            "status": "ACTIVE",
            "confirmations": confirmation_details,
            "result_time": None,
            "result_price": None,
            "profit_percentage": None,
            "volatility_level": volatility_level,
            "fib_level": fib_level
        }

        # Veri doğrulama
        if not self._validate_signal_data(signal_data):
            logger.error(f"Sinyal kaydedilirken validasyon hatası: {symbol}")
            return ""

        # CSV'ye kaydet
        with open(self.signals_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS)

            # confirmations alanını string'e çevir
            row_data = signal_data.copy()
            row_data['confirmations'] = str(confirmation_details)

            writer.writerow(row_data)

        # Aktif sinyallere ekle
        self.active_signals[signal_id] = signal_data

        # Metrikleri güncelle
        self._update_metrics(new_signal=True)

        logger.info(f"Yeni sinyal kaydedildi: {signal_id} ({symbol}, {timeframe}, {direction}, Score: {net_score})")
        return signal_id

    def check_active_signals(self, current_prices: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        Aktif sinyalleri kontrol eder ve tamamlananları sonuç dosyasına kaydeder.

        Args:
            current_prices: Mevcut sembol fiyatları {sembol: fiyat}

        Returns:
            List[Dict[str, Any]]: Tamamlanan işlemler listesi
        """
        if not self.active_signals:
            return []

        # İstatistik güncellemeden önce dosyaları yedekle
        # Günlük yedek almak için son yedeklemeden 1 günden fazla zaman geçmiş mi?
        time_since_last_check = datetime.now() - self.last_check_time
        if time_since_last_check > timedelta(days=1):
            self._backup_stats_files()
            self.last_check_time = datetime.now()

        completed_trades = []
        signals_to_remove = []

        for signal_id, signal_data in self.active_signals.items():
            symbol = signal_data.get("symbol")
            if symbol not in current_prices:
                logger.warning(f"Sembol fiyatı bulunamadı: {symbol}")
                continue

            current_price = current_prices[symbol]
            direction = signal_data.get("direction")
            sl_price = signal_data.get("sl_price")
            tp1_price = signal_data.get("tp1_price", signal_data.get("tp_price"))  # Geriye uyumluluk için
            tp2_price = signal_data.get("tp2_price")
            tp3_price = signal_data.get("tp3_price")
            entry_price = signal_data.get("entry_price")

            # Sonuç kontrolü
            result = self._check_signal_result(direction, current_price, entry_price, sl_price, tp1_price, tp2_price, tp3_price)

            if result and result != "ACTIVE":
                # İşlemi tamamla
                signal_data["status"] = result
                signal_data["result_time"] = datetime.now().isoformat()
                signal_data["result_price"] = current_price
                # Kar/zarar hesapla
                profit_percentage = self._calculate_profit(
                    direction, entry_price, current_price
                )
                signal_data["profit_percentage"] = profit_percentage
                # Sonuç dosyasına kaydet
                with open(self.results_file, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS)
                    # Önce confirmation_details'ı stringe çevir
                    row_data = signal_data.copy()
                    row_data['confirmations'] = str(signal_data.get('confirmations', []))
                    writer.writerow(row_data)
                # Sinyali kaldır listesine ekle
                signals_to_remove.append(signal_id)
                # Tamamlanan işlemler listesine ekle
                completed_trades.append(signal_data)
                logger.info(f"İşlem tamamlandı: {signal_id} ({symbol}, {result}, %{profit_percentage:.2f})")

        # Tamamlanan işlemleri aktif sinyallerden kaldır
        for signal_id in signals_to_remove:
            if signal_id in self.active_signals:
                del self.active_signals[signal_id]

        # Ana CSV'yi güncelle (ACTIVE olmayan sinyalleri kaldır)
        self._update_signals_csv()

        # Metrikleri güncelle
        if completed_trades:
            self._update_metrics(completed_trades=completed_trades)

        return completed_trades

    def _update_signals_csv(self):
        """Aktif sinyaller tablosunu günceller (CSV'de)"""
        try:
            # CSV'yi oku ve aktif sinyalleri kaldır
            signals_df = pd.read_csv(self.signals_file)

            # Aktif olmayan sinyaller (tamamlanmış olanlar)
            non_active = signals_df[signals_df['status'] != 'ACTIVE']

            # Aktif sinyaller (hazıfaza)
            active_rows = []
            for signal_id, signal_data in self.active_signals.items():
                row_data = signal_data.copy()
                row_data['confirmations'] = str(signal_data.get('confirmations', []))
                active_rows.append(row_data)

            if active_rows:
                # Yeni aktif sinyaller dataframe'i
                active_df = pd.DataFrame(active_rows)

                # Eski ve yeni dataframe'leri birleştir
                updated_df = pd.concat([non_active, active_df])
            else:
                updated_df = non_active

            # CSV'yi yaz
            updated_df.to_csv(self.signals_file, index=False)

        except Exception as e:
            logger.error(f"CSV güncellenirken hata: {e}")

    def _check_signal_result(self, direction: str, current_price: float,
                           entry_price: float, sl_price: float, tp1_price: float,
                           tp2_price: Optional[float] = None, tp3_price: Optional[float] = None) -> str:
        """
        Sinyal sonucunu kontrol eder (TP1, TP2, TP3, SL veya ACTIVE)
        Not: tp1_price parametresi, geriye uyumluluk için tp_price olarak adlandırılmıştır.

        Returns:
            str: "TP1", "TP2", "TP3", "SL" veya "ACTIVE"
        """
        if direction == "BULL":
            if current_price <= sl_price:
                return "SL"
            elif tp3_price is not None and current_price >= tp3_price:
                return "TP3"
            elif tp2_price is not None and current_price >= tp2_price:
                return "TP2"
            elif current_price >= tp1_price:
                return "TP1"
        elif direction == "BEAR":
            if current_price >= sl_price:
                return "SL"
            elif tp3_price is not None and current_price <= tp3_price:
                return "TP3"
            elif tp2_price is not None and current_price <= tp2_price:
                return "TP2"
            elif current_price <= tp1_price:
                return "TP1"

        return "ACTIVE"

    def _calculate_profit(self, direction: str, entry_price: float, exit_price: float) -> float:
        """
        Kar/zarar yüzdesini hesaplar

        Returns:
            float: İşlemin kar/zarar yüzdesi (+ kar, - zarar)
        """
        if entry_price == 0:
            return 0.0

        if direction == "BULL":
            return ((exit_price - entry_price) / entry_price) * 100
        elif direction == "BEAR":
            return ((entry_price - exit_price) / entry_price) * 100

        return 0.0

    def _update_metrics(self, completed_trades: List[Dict[str, Any]] = None, new_signal: bool = False):
        """
        Performans metriklerini günceller

        Args:
            completed_trades: Tamamlanan işlemler listesi
            new_signal: Yeni bir sinyal eklendiyse True
        """
        try:
            # Mevcut metrikleri yükle
            with open(self.metrics_file, 'r', encoding='utf-8') as f:
                metrics = json.load(f)

            # Eksik TP alanlarını kontrol et ve ekle
            if "tp1_hits" not in metrics:
                metrics["tp1_hits"] = 0
            if "tp2_hits" not in metrics:
                metrics["tp2_hits"] = 0
            if "tp3_hits" not in metrics:
                metrics["tp3_hits"] = 0
            if "tp1_rate" not in metrics:
                metrics["tp1_rate"] = 0.0
            if "tp2_rate" not in metrics:
                metrics["tp2_rate"] = 0.0
            if "tp3_rate" not in metrics:
                metrics["tp3_rate"] = 0.0

            # Yeni sinyal
            if new_signal:
                metrics["total_signals"] += 1

            # Tamamlanan işlem yoksa, kaydet ve çık
            if not completed_trades:
                metrics["last_updated"] = datetime.now().isoformat()
                with open(self.metrics_file, 'w', encoding='utf-8') as f:
                    json.dump(metrics, f, indent=4)
                return

            # Tamamlanan işlemler var, metrikleri güncelle
            for trade in completed_trades:
                # İşlem timeframe'i kontrol et - sadece ana timeframe işlemlerini say
                if trade.get("timeframe") != self.main_timeframe:
                    logger.debug(f"İşlem {trade.get('signal_id')} ana timeframe'de olmadığı için metrikler güncellenmedi")
                    continue

                metrics["completed_trades"] += 1

                status = trade.get("status", "")
                profit = trade.get("profit_percentage", 0.0)

                # Başarılı işlem sayısı ve TP seviyelerine göre sayım
                if status in ["TP1", "TP2", "TP3", "TP"]:  # TP, geriye uyumluluk için
                    metrics["successful_trades"] += 1

                    # TP seviyelerine göre ayrı sayım
                    if status == "TP1":
                        metrics["tp1_hits"] += 1
                    elif status == "TP2":
                        metrics["tp2_hits"] += 1
                    elif status == "TP3":
                        metrics["tp3_hits"] += 1
                else:
                    metrics["failed_trades"] += 1

                # Toplam kar
                metrics["total_profit_percentage"] += profit

                # Ortalama kar
                metrics["avg_profit_percentage"] = metrics["total_profit_percentage"] / metrics["completed_trades"]

                # Başarı oranı
                if metrics["completed_trades"] > 0:
                    metrics["success_rate"] = (metrics["successful_trades"] / metrics["completed_trades"]) * 100

                    # TP seviyelerine göre başarı oranları
                    metrics["tp1_rate"] = (metrics["tp1_hits"] / metrics["completed_trades"]) * 100
                    metrics["tp2_rate"] = (metrics["tp2_hits"] / metrics["completed_trades"]) * 100
                    metrics["tp3_rate"] = (metrics["tp3_hits"] / metrics["completed_trades"]) * 100

                    # En iyi/kötü işlem
                symbol = trade.get("symbol", "")
                if metrics["best_trade"] is None or profit > metrics["best_trade"].get("profit", 0):
                        metrics["best_trade"] = {
                        "symbol": symbol,
                        "profit": profit,
                        "time": trade.get("result_time", "")
                    }

                if metrics["worst_trade"] is None or profit < metrics["worst_trade"].get("profit", 0):
                        metrics["worst_trade"] = {
                        "symbol": symbol,
                        "profit": profit,
                        "time": trade.get("result_time", "")
                    }

                # En iyi sembol ve teyit hesaplamaları da yapılabilir
                # ...

            # Son güncelleme zamanı
            metrics["last_updated"] = datetime.now().isoformat()

            # Metrikleri kaydet
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, indent=4)

            logger.debug(f"Performans metrikleri güncellendi")

        except Exception as e:
            logger.error(f"Metrikler güncellenirken hata: {e}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Mevcut performans metriklerini döndürür

        Returns:
            Dict[str, Any]: Performans metrikleri
        """
        try:
            with open(self.metrics_file, 'r', encoding='utf-8') as f:
                metrics = json.load(f)

            # Eksik TP alanlarını kontrol et ve ekle
            if "tp1_hits" not in metrics:
                metrics["tp1_hits"] = 0
            if "tp2_hits" not in metrics:
                metrics["tp2_hits"] = 0
            if "tp3_hits" not in metrics:
                metrics["tp3_hits"] = 0
            if "tp1_rate" not in metrics:
                metrics["tp1_rate"] = 0.0
            if "tp2_rate" not in metrics:
                metrics["tp2_rate"] = 0.0
            if "tp3_rate" not in metrics:
                metrics["tp3_rate"] = 0.0

            return metrics
        except Exception as e:
            logger.error(f"Metrikler okunurken hata: {e}")
            return {}

    def get_active_signals(self) -> List[Dict[str, Any]]:
        """
        Aktif sinyalleri döndürür (sadece ana timeframe)
        Her sembol+yön kombinasyonu için en son sinyal dikkate alınır"""
        # Önce ana timeframe'e göre filtrele
        filtered_signals = [
            signal for signal in self.active_signals.values()
            if signal.get("timeframe") == self.main_timeframe
        ]
        # Sonra her sembol+yön kombinasyonu için en son sinyali seç
        return self._get_unique_latest_signals(filtered_signals)

    def get_active_signals_count(self) -> int:
        """Aktif sinyal sayısını döndürür (sadece ana timeframe)
        Her sembol+yön kombinasyonu için en son sinyal dikkate alınır"""
        return len(self.get_active_signals())

    def _get_unique_latest_signals(self, signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Her sembol+yön kombinasyonu için en son sinyali döndürür

        Args:
            signals: İşlenecek sinyal listesi

        Returns:
            Her sembol+yön kombinasyonu için en son sinyali içeren liste
        """
        # Her sembol+yön kombinasyonunu anahtar olarak kullanalım
        latest_signals_map = {}

        for signal in signals:
            symbol = signal.get('symbol')
            direction = signal.get('direction')
            entry_time = signal.get('entry_time')

            if not (symbol and direction and entry_time):
                continue

            key = f"{symbol}_{direction}"

            # Bu anahtar için daha önce sinyal var mı kontrol et
            if key in latest_signals_map:
                # Mevcut sinyalin giriş zamanını al
                existing_entry_time = latest_signals_map[key].get('entry_time')

                # Eğer yeni sinyal daha yeniyse, güncelle
                if entry_time > existing_entry_time:
                    latest_signals_map[key] = signal
            else:
                # Bu sembol+yön için ilk sinyal
                latest_signals_map[key] = signal

        # Değerleri liste olarak döndür
        return list(latest_signals_map.values())

    def validate_reporting_consistency(self) -> Dict[str, Any]:
        """Raporlama tutarlılığını doğrular"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "is_consistent": True,
            "issues": [],  # "errors" yerine "issues" kullanılıyor
            "signals_in_memory": len(self.active_signals),
            "signals_in_csv": 0,
            "unique_signals_for_main_timeframe": len(self.get_active_signals()),
            "total_trades_in_memory": 0,  # self.trade_results tanımlı değil, 0 olarak ayarlandı
            "total_trades_in_csv": 0,
        }

        try:
            # Aktif sinyal sayısı
            report["active_signals_count"] = self.get_active_signals_count()

            # CSV dosyasını oku
            if os.path.exists(self.signals_file):
                signals_df = pd.read_csv(self.signals_file)

                # Ana timeframe'e göre filtrele
                signals_df = signals_df[signals_df['timeframe'] == self.main_timeframe]

                # Toplam sinyal sayısı
                report["total_signals_in_csv"] = len(signals_df)

                # Aktif sinyaller
                csv_active = signals_df[signals_df['status'] == 'ACTIVE']
                report["active_signals_in_csv"] = len(csv_active)

                # Tutarsızlık kontrolü - aktif sinyal sayıları eşleşmeli
                if report["active_signals_count"] != report["active_signals_in_csv"]:
                    report["is_consistent"] = False
                    report["issues"].append(f"Aktif sinyal sayıları uyuşmuyor: Hafızada {report['active_signals_count']}, CSV'de {report['active_signals_in_csv']}")

                # Tamamlanan işlemler
                completed_df = signals_df[signals_df['status'].isin(['TP1', 'TP2', 'TP3', 'TP', 'SL'])]
                report["completed_trades_count"] = len(completed_df)

                # Sonuç dosyasını da kontrol et
                if os.path.exists(self.results_file):
                    results_df = pd.read_csv(self.results_file)

                    # Ana timeframe'e göre filtrele
                    results_df = results_df[results_df['timeframe'] == self.main_timeframe]

                    report["completed_trades_in_results"] = len(results_df)

                    # Sonuç sayıları eşleşmeli
                    if report["completed_trades_count"] != report["completed_trades_in_results"]:
                        report["is_consistent"] = False
                        report["issues"].append(f"Tamamlanan işlem sayıları uyuşmuyor: Sinyaller CSV'de {report['completed_trades_count']}, Sonuçlar CSV'de {report['completed_trades_in_results']}")

            # Metrik dosyasını kontrol et
            if os.path.exists(self.metrics_file):
                with open(self.metrics_file, 'r', encoding='utf-8') as f:
                    metrics = json.load(f)

                report["metrics_completed_trades"] = metrics.get("completed_trades", 0)
                report["metrics_total_signals"] = metrics.get("total_signals", 0)

                # Metrikler ve CSV dosyaları tutarlı olmalı
                if report["completed_trades_in_results"] != report["metrics_completed_trades"]:
                    report["is_consistent"] = False
                    report["issues"].append(f"Metrik ve CSV tamamlanan işlem sayıları uyuşmuyor: Metrikler'de {report['metrics_completed_trades']}, CSV'de {report['completed_trades_in_results']}")

            logger.info(f"Raporlama tutarlılık kontrolü: {'BAŞARILI' if report['is_consistent'] else 'HATALI'}")
            if not report["is_consistent"]:
                for issue in report["issues"]:
                    logger.warning(f"Tutarlılık sorunu: {issue}")

            return report

        except Exception as e:
            logger.error(f"Raporlama tutarlılığı kontrol edilirken hata: {e}")
            report["is_consistent"] = False
            report["issues"].append(f"Kontrol sırasında hata: {str(e)}")
            return report

    def add_signal(self, signal_data: Dict[str, Any]) -> str:
        """
        Yeni bir sinyal ekler ve CSV dosyasına kaydeder.

        Args:
            signal_data: Sinyal verisi

        Returns:
            str: Sinyal ID
        """
        # Sinyal verilerini doğrula
        if not self._validate_signal_data(signal_data):
            logger.warning(f"Geçersiz sinyal verisi: {signal_data}")
            return ""

        # Günlük limit kontrolü - belirli bir sembol için günde max 2 sinyal
        symbol = signal_data["symbol"]
        direction = signal_data["direction"]
        timeframe = signal_data.get("timeframe", self.main_timeframe)

        # Sadece ana timeframe için günlük limit uygula
        if timeframe == self.main_timeframe and not self._check_daily_signal_limit(symbol, direction):
            logger.info(f"Günlük sinyal limiti aşıldı: {symbol} {direction}")
            return ""

        # Sinyal ID oluştur
        signal_id = self.generate_signal_id(symbol, direction)
        signal_data["signal_id"] = signal_id
        signal_data["entry_time"] = datetime.now().isoformat()
        signal_data["status"] = "ACTIVE"
        signal_data["timeframe"] = timeframe

        # TP1, TP2, TP3 alanlarını kontrol et ve gerekirse ekle
        if "tp_price" in signal_data and "tp1_price" not in signal_data:
            signal_data["tp1_price"] = signal_data["tp_price"]

        # Smart entry seviyelerinden TP seviyelerini al (eğer varsa)
        entry_levels = signal_data.get("entry_levels", {})
        if entry_levels:
            if "tp1_price" not in signal_data and "tp1" in entry_levels:
                signal_data["tp1_price"] = entry_levels["tp1"]
            if "tp2_price" not in signal_data and "tp2" in entry_levels:
                signal_data["tp2_price"] = entry_levels["tp2"]
            if "tp3_price" not in signal_data and "tp3" in entry_levels:
                signal_data["tp3_price"] = entry_levels["tp3"]

        # Volatilite seviyesi ve Fibonacci seviyesi alanlarını kontrol et ve gerekirse ekle
        if "volatility_level" not in signal_data:
            signal_data["volatility_level"] = "unknown"

        if "fib_level" not in signal_data:
            signal_data["fib_level"] = None

        # CSV'ye kaydet
        with open(self.signals_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS)

            # confirmations alanını string'e çevir
            row_data = signal_data.copy()
            row_data['confirmations'] = str(signal_data.get('confirmations', []))

            writer.writerow(row_data)

        # Aktif sinyallere ekle
        self.active_signals[signal_id] = signal_data

        # Metrikleri güncelle
        self._update_metrics(new_signal=True)

        logger.info(f"Yeni sinyal eklendi: {signal_id} ({symbol}, {direction}, {timeframe})")
        return signal_id

    def _check_daily_signal_limit(self, symbol: str, direction: str) -> bool:
        """
        Sembol ve yön için günlük sinyal limitini kontrol eder

        Returns:
            bool: Limit aşılmamışsa True
        """
        try:
            # Eğer signals dosyası yoksa, limit yok
            if not os.path.exists(self.signals_file):
                return True

            # CSV dosyasını oku
            signals_df = pd.read_csv(self.signals_file)

            # Bugünkü tarihi al
            today = datetime.now().date()

            # Bugün eklenen sinyalleri bul
            signals_df['entry_date'] = pd.to_datetime(signals_df['entry_time']).dt.date
            today_signals = signals_df[
                (signals_df['entry_date'] == today) &
                (signals_df['symbol'] == symbol) &
                (signals_df['direction'] == direction) &
                (signals_df['timeframe'] == self.main_timeframe)
            ]

            # Günlük limit (sembol/yön başına)
            return len(today_signals) < 2

        except Exception as e:
            logger.error(f"Günlük sinyal limiti kontrolünde hata: {e}")
            # Hata durumunda sinyali kabul et
            return True

    def generate_performance_report(self) -> str:
        """
        Performans raporu oluşturur

        Returns:
            str: Performans raporu metni
        """
        try:
            # Metrikleri oku
            metrics = self.get_performance_metrics()

            # Rapor metni
            report = []
            report.append("📊 *PERFORMANCE REPORT* 📊")
            report.append(f"📅 Report Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append("")

            # Ana timeframe'i belirt
            report.append(f"🕒 Timeframe: {self.main_timeframe}m")
            report.append("")

            report.append("📈 *OVERALL STATISTICS*")
            report.append(f"Total Signals: {metrics.get('total_signals', 0)}")
            report.append(f"Completed Trades: {metrics.get('completed_trades', 0)}")

            if metrics.get('success_rate'):
                report.append(f"Success Rate: {metrics.get('success_rate', 0):.2f}%")

            if metrics.get('total_profit_percentage'):
                report.append(f"Total Profit: {metrics.get('total_profit_percentage', 0):.2f}%")

            if metrics.get('avg_profit_percentage'):
                report.append(f"Average Profit: {metrics.get('avg_profit_percentage', 0):.2f}%")

            report.append("")

            # TP seviyelerine göre başarı oranları
            report.append("🎯 *TAKE PROFIT STATISTICS*")
            report.append(f"TP1 Hits: {metrics.get('tp1_hits', 0)} ({metrics.get('tp1_rate', 0):.2f}%)")
            report.append(f"TP2 Hits: {metrics.get('tp2_hits', 0)} ({metrics.get('tp2_rate', 0):.2f}%)")
            report.append(f"TP3 Hits: {metrics.get('tp3_hits', 0)} ({metrics.get('tp3_rate', 0):.2f}%)")
            report.append("")

            # En iyi/kötü işlemler
            if metrics.get("best_trade"):
                report.append("🏆 *BEST TRADE*")
                best = metrics["best_trade"]
                report.append(f"Symbol: {best.get('symbol', '')}")
                report.append(f"Profit: {best.get('profit', 0):.2f}%")
                report.append(f"Time: {best.get('time', '')}")
                report.append("")

            if metrics.get("worst_trade"):
                report.append("📉 *WORST TRADE*")
                worst = metrics["worst_trade"]
                report.append(f"Symbol: {worst.get('symbol', '')}")
                report.append(f"Loss: {worst.get('profit', 0):.2f}%")
                report.append(f"Time: {worst.get('time', '')}")
                report.append("")

            # Aktif sinyaller
            active_signals = self.get_active_signals()
            if active_signals:
                report.append("🔄 *ACTIVE SIGNALS*")
                for signal in active_signals[:5]:  # En fazla 5 aktif sinyal göster
                    symbol = signal.get("symbol", "")
                    direction = signal.get("direction", "")
                    entry_price = signal.get("entry_price", 0)
                    entry_time = signal.get("entry_time", "")
                    formatted_time = ""
                    try:
                        entry_dt = datetime.fromisoformat(entry_time)
                        formatted_time = entry_dt.strftime("%Y-%m-%d %H:%M")
                    except:
                        formatted_time = entry_time

                    report.append(f"{symbol} ({direction}): {entry_price} @ {formatted_time}")

                if len(active_signals) > 5:
                    report.append(f"...ve {len(active_signals) - 5} sinyal daha")

                report.append("")

            # Tutarlılık kontrolü
            consistency = self.validate_reporting_consistency()
            if not consistency.get("is_consistent", True):
                report.append("⚠️ *CONSISTENCY ISSUES*")
                for issue in consistency.get("issues", []):
                    report.append(f"- {issue}")
                report.append("")

            return "\n".join(report)

        except Exception as e:
            logger.error(f"Performans raporu oluşturulurken hata: {e}")
            return f"❌ Error generating performance report: {e}"

    def get_daily_signals(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Bugün oluşturulan sinyalleri döndürür (sadece ana timeframe)

        Args:
            limit: Maksimum sinyal sayısı

        Returns:
            List[Dict[str, Any]]: Bugünkü sinyaller
        """
        try:
            # Signals CSV dosyasını oku
            if not os.path.exists(self.signals_file):
                return []

            signals_df = pd.read_csv(self.signals_file)

            # Ana timeframe'e göre filtrele
            signals_df = signals_df[signals_df['timeframe'] == self.main_timeframe]

            # Bugünün tarihini al
            today = datetime.now().date()

            # Bugün oluşturulan sinyalleri filtrele
            signals_df['entry_date'] = pd.to_datetime(signals_df['entry_time']).dt.date
            today_signals = signals_df[signals_df['entry_date'] == today]

            # En son eklenen sinyalleri al
            today_signals = today_signals.sort_values('entry_time', ascending=False).head(limit)

            # Dict listesine dönüştür
            signals = []
            for _, row in today_signals.iterrows():
                signal_data = row.to_dict()

                # confirmations alanını string'den listeye çevir
                if isinstance(signal_data.get('confirmations'), str):
                    try:
                        signal_data['confirmations'] = eval(signal_data['confirmations'])
                    except:
                        signal_data['confirmations'] = []

                signals.append(signal_data)

            return signals

        except Exception as e:
            logger.error(f"Günlük sinyaller alınırken hata: {e}")
            return []

    def get_top_scores(self, min_score: float = 1.0, max_count: int = 10, direction: str = None) -> List[Dict[str, Any]]:
        """
        En yüksek skorlu sinyalleri döndürür

        Args:
            min_score: Minimum skor
            max_count: Maksimum sinyal sayısı
            direction: BULL veya BEAR (None ise her ikisi)

        Returns:
            List[Dict[str, Any]]: En yüksek skorlu sinyaller
        """
        try:
            # Aktif sinyaller arasından en yüksek skorluları seç
            scores = []
            for signal in self.active_signals.values():
                # Ana timeframe'de değilse atla
                if signal.get("timeframe") != self.main_timeframe:
                    continue

                # Yön kontrolü
                if direction and signal.get("direction") != direction:
                    continue

                # Minimum skor kontrolü
                score = signal.get("net_score", 0)
                if score < min_score:
                    continue

                scores.append(signal)

            # Skora göre sırala
            scores.sort(key=lambda x: x.get("net_score", 0), reverse=True)

            # Maksimum sayıda döndür
            return scores[:max_count]

        except Exception as e:
            logger.error(f"En yüksek skorlu sinyaller alınırken hata: {e}")
            return []

    def reset_statistics(self, keep_backup: bool = True) -> bool:
        """
        İstatistik dosyalarını sıfırlar

        Args:
            keep_backup: True ise mevcut dosyaları yedekler

        Returns:
            bool: Başarılı ise True
        """
        try:
            # Önce yedek al
            if keep_backup:
                self._backup_stats_files()

            # Aktif sinyalleri temizle
            self.active_signals = {}

            # CSV dosyalarını sıfırla (başlık satırlarını yeniden ekle)
            with open(self.signals_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS)
                writer.writeheader()

            with open(self.results_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=SIGNAL_FIELDS)
                writer.writeheader()

            # Metrikleri sıfırla
            initial_metrics = {
                "total_signals": 0,
                "completed_trades": 0,
                "successful_trades": 0,
                "failed_trades": 0,
                "success_rate": 0,
                "avg_profit_percentage": 0,
                "total_profit_percentage": 0,
                "tp1_hits": 0,
                "tp2_hits": 0,
                "tp3_hits": 0,
                "tp1_rate": 0.0,
                "tp2_rate": 0.0,
                "tp3_rate": 0.0,
                "best_trade": None,
                "worst_trade": None,
                "last_updated": datetime.now().isoformat()
            }

            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(initial_metrics, f, indent=4)

            logger.info("İstatistikler başarıyla sıfırlandı")
            return True

        except Exception as e:
            logger.error(f"İstatistikler sıfırlanırken hata: {e}")
            return False