# Automaton v1.3 - <PERSON><PERSON> ve Çalışma Mantığı

B<PERSON>, Automaton v1.3 Finansal Analiz ve Pivot Tespit Sistemi'nin genel mimarisini, bileşenlerini ve çalışma mantığını açıklar.

## Genel Bakış

Bu sistem, finansal veri serilerinde teknik analiz yapmak ve önemli dönüş noktalarını tespit etmek için tasarlanmıştır. Sistem, TradingView'daki pivot ve zigzag göstergelerine benzer bir mantıkla çalışır ancak özelleştirilebilir ve genişletilebilir bir yapıdadır.

## Sistem Mimarisi

```
                  +----------------+
                  |                |
                  |     main.py    |
                  |                |
                  +--------+-------+
                           |
          +----------------+-----------------+------------------+
          |                |                 |                  |
+---------v--------+ +-----v------+ +--------v--------+
|                  | |            | |                 |
| bybit_client.py  | | patterns.py| | scoring_        |
|                  | |            | | system.py       |
+---------+--------+ +-----+------+ +------+-------+
          |                |                 |
          |                |                 |
          |       +--------v--------+        |
          +------>|                 |<-------+
                  | pivot_analyzer.py |
                  |                 |
                  +--------+--------+
                           |
                  +--------v---------+
                  |                  |
                  | divergence_      |
                  | analyzer.py      |
                  +--------+---------+
                           |
          +-----------------+----------------+-----------------+
          |                 |                |                 |
+---------v--------+ +------v------+ +-------v---------+ +----v----------+
|                  | |             | |                 | |               |
| fvrp_analyzer.py | | npoc_       | | timeframe_      | | daily_bias_   |
|                  | | analyzer.py | | levels_         | | analyzer.py   |
+------------------+ +-------------+ | analyzer.py     | |               |
                                     +-----------------+ +----+----------+
                                                             |
                                                   +---------v----------+
                                                   |                    |
                                                   | supertrend_        |
                                                   | analyzer.py        |
                                                   +--------------------+
```

## Ana Bileşenler

### 1. Veri Toplama Modülü
- **Dosya**: `bybit_client.py`
- **Amaç**: Bybit API'sinden OHLCV verileri ve funding oranları çeker
- **Temel Sınıf**: `BybitClient`
- **Temel Fonksiyonlar**:
  - `fetch_klines()`: Belirli bir sembol ve zaman dilimi için OHLCV verilerini çeker
  - `fetch_funding_rate()`: Bir sembol için funding oranını çeker

### 2. Pivot Analiz Modülü
- **Dosya**: `pivot_analyzer.py`
- **Amaç**: Pivot noktalarını ve swing patternleri tespit eder
- **Temel Sınıf**: `PivotAnalyzer`
- **Temel Fonksiyonlar**:
  - `find_pivots()`: `scipy.signal.find_peaks` kullanarak yüksek ve düşük pivot noktalarını tespit eder
  - `identify_swings()`: Pivot noktalarından swing patternlerini (HH, HL, LH, LL) oluşturur
  - `detect_patterns()`: Tanımlı formasyonları tespit eder
  - `calculate_vwap()`: VWAP hesaplaması yapar

### 3. Divergence Analiz Modülü
- **Dosya**: `divergence_analyzer.py`
- **Amaç**: Fiyat ve göstergeler arasındaki uyumsuzlukları tespit eder
- **Temel Sınıf**: `DivergenceAnalyzer`
- **Temel Fonksiyonlar**:
  - `calculate_indicators()`: RSI, MACD, Stochastic gibi teknik göstergeleri hesaplar
  - `detect_hidden_divergences()`: Bullish ve bearish divergence'ları tespit eder

### 4. FVRP Analiz Modülü
- **Dosya**: `fvrp_analyzer.py`
- **Amaç**: Hacim profili ve VOC (Volume by Price) analizi yapar
- **Temel Sınıf**: `FVRPAnalyzer`
- **Temel Fonksiyonlar**:
  - `calculate_volume_profile()`: POC, VAH, VAL değerlerini hesaplar
  - `analyze_historical_fvrp()`: Geçmiş FVRP değerlerini analiz eder

### 5. Naked POC Analiz Modülü
- **Dosya**: `npoc_analyzer.py`
- **Amaç**: Fiyat tarafından test edilmemiş POC noktalarını tespit eder
- **Temel Sınıf**: `NPOCAnalyzer`
- **Temel Fonksiyonlar**:
  - `check_naked_pocs()`: POC'ların "naked" olup olmadığını kontrol eder
  - `analyze_all_symbols()`: Tüm semboller için naked POC analizi yapar

### 6. Zaman Dilimi Seviye Analiz Modülü
- **Dosya**: `timeframe_levels_analyzer.py`
- **Amaç**: Günlük, haftalık ve aylık açılış ve denge seviyelerini tespit eder
- **Temel Sınıf**: `TimeframeLevelsAnalyzer`
- **Temel Fonksiyonlar**:
  - `analyze_timeframe_levels()`: DO, WO, MO, EqD, EqW, EqM, MR ve MREq seviyelerini hesaplar
  - `analyze_all_symbols()`: Tüm semboller için zaman dilimi seviyelerini analiz eder
  - `get_all_levels()`: Tüm semboller için hesaplanmış zaman dilimi seviyelerini döndürür

### 7. Örüntü Tanımlama Modülü
- **Dosya**: `patterns.py`
- **Amaç**: Çeşitli fiyat formasyonlarının tanımlarını içerir
- **Temel Veri Yapıları**:
  - `BULL_PATTERNS`: Boğa piyasası formasyonları
  - `BEAR_PATTERNS`: Ayı piyasası formasyonları
  - Her formasyon için pivot dizilişleri tanımlanır (örn. "HL-HH-HL")

### 8. Fibonacci Analiz Modülü
- **Dosya**: `fibonacci_analyzer.py`
- **Amaç**: Pivot noktaları arasında Fibonacci seviyeleri ve altın bölgeler hesaplar
- **Temel Sınıf**: `FibonacciAnalyzer`
- **Temel Fonksiyonlar**:
  - `calculate_fibonacci_levels()`: İki swing noktası arasında Fibonacci seviyelerini hesaplar
  - `identify_golden_zones()`: Altın Oran bölgelerini tespit eder

### 9. Order Block Analiz Modülü
- **Dosya**: `order_block_analyzer.py`
- **Amaç**: Fiyat hareketlerinde önemli sipariş bloklarını tespit eder
- **Temel Sınıf**: `OrderBlockAnalyzer`
- **Temel Fonksiyonlar**:
  - `find_latest_order_blocks()`: En son boğa ve ayı sipariş bloklarını tespit eder
  - Temel olarak 12 saatlik (720) timeframe için çalışır

### 10. Break of Structure Analiz Modülü
- **Dosya**: `bos_analyzer.py`
- **Amaç**: Piyasa yapısındaki kırılmaları tespit eder
- **Temel Sınıf**: `BOSAnalyzer`
- **Temel Fonksiyonlar**:
  - `detect_bos()`: Son 3 swing noktasını kullanarak BOS desenlerini tespit eder
  - `analyze_symbol()`: Bir sembol ve zaman dilimi için BOS analizi yapar
  - 1 saatlik (60) timeframe için analiz yapılmaz

### 11. SFP Analiz Modülü
- **Dosya**: `sfp_analyzer.py`
- **Amaç**: Swing Failure Pattern (SFP) oluşumlarını tespit eder
- **Temel Sınıf**: `SFPAnalyzer`
- **Temel Fonksiyonlar**:
  - `_detect_sfp_on_dataframe()`: Verilen DataFrame üzerinde SFP desenlerini tespit eder
  - `analyze_symbol()`: Bir sembol ve zaman dilimi için SFP analizi yapar

### 12. Daily Bias Analiz Modülü
- **Dosya**: `daily_bias_analyzer.py`
- **Amaç**: Günlük zaman diliminde piyasa yönünü, çeşitli faktörleri kullanarak analiz etmek. Bu sayede diğer zaman dilimlerindeki analizler için temel referans noktası oluşturmak.
- **Temel Sınıf**: `DailyBiasAnalyzer`
- **Temel Fonksiyonlar**:
  - `determine_bias(symbol)`: Sembol için günlük yönü belirler
  - `get_bias(symbol, timeframe)`: Belirli bir zaman dilimi için önceden belirlenmiş yönü döndürür
  - `_check_ema_positions(candles)`: EMA konumlarını analiz eder
  - `_check_volume_trend(candles)`: Hacim trendini analiz eder
  - `_check_price_patterns(candles)`: Fiyat paternlerini analiz eder
  - `_initialize_bias_cache()`: Bias önbelleğini başlatır

### 13. SuperTrend Analiz Modülü
- **Dosya**: `supertrend_analyzer.py` ve `supertrend.py`
- **Amaç**: SuperTrend göstergesini hesaplamak, trend yönünü belirlemek ve mevcut fiyatın SuperTrend değerine olan uzaklığını analiz etmek.
- **Temel Sınıf**: `SuperTrendAnalyzer`
- **Temel Fonksiyonlar**:
  - `analyze_candles(symbol, timeframe, candles)`: Mum verisi üzerinde SuperTrend analizi yapar
  - `get_supertrend_value(candles)`: SuperTrend değerini hesaplar
  - `calculate_distance_to_price(candles, st_value, trend)`: SuperTrend değeri ile fiyat arasındaki uzaklığı hesaplar
  - `format_price_with_precision(price, precision)`: Fiyatı belirli bir hassasiyetle formatlar

### 14. Puanlama Sistemi Modülü
- **Dosya**: `scoring_system.py`
- **Amaç**: Çeşitli analiz sonuçlarını birleştirerek finansal enstrümanları puanlar
- **Temel Sınıf**: `ScoringSystem`
- **Temel Fonksiyonlar**:
  - `calculate_score()`: Farklı analizlerden gelen verileri kullanarak puan hesaplar
  - `generate_score_report()`: Puanlama sonuçlarını rapor formatında döndürür
  - `reset_scores()`: Puanlama sistemini sıfırlar
  - `add_supertrend_score()`: SuperTrend analizinden puanı hesaplar
  - `add_daily_bias_score()`: Daily Bias analizinden puanı hesaplar
  - `add_pattern_score()`: Tespit edilen formasyonlardan puanı hesaplar
  - `add_divergence_score()`: Divergence analizinden puanı hesaplar
  - `add_fibonacci_score()`: Fibonacci seviye analizinden puanı hesaplar

### 15. FVG Analiz Modülü
- **Dosya**: `fvg_analyzer.py`
- **Amaç**: Fair Value Gap (FVG) bölgelerini tespit eder ve analiz eder
- **Temel Sınıf**: `FVGAnalyzer`
- **Temel Fonksiyonlar**:
  - `find_fvgs()`: Mum verilerinde FVG bölgelerini tespit eder
  - `_is_bullish_fvg()`: Bullish FVG koşullarını kontrol eder
  - `_is_bearish_fvg()`: Bearish FVG koşullarını kontrol eder
  - `_check_fvg_filled()`: FVG'nin doldurulup doldurulmadığını kontrol eder

### 16. Premium/Discount Analiz Modülü
- **Dosya**: `premium_discount_analyzer.py`
- **Amaç**: Fiyatın günlük pivot noktalarına göre premium/discount bölgede olup olmadığını analiz eder
- **Temel Sınıf**: `PremiumDiscountAnalyzer`
- **Temel Fonksiyonlar**:
  - `calculate_daily_premium_discount()`: Günlük EQ seviyesine göre premium/discount durumunu hesaplar
  - `analyze_symbol()`: Sembol için premium/discount analizi yapar

### 17. Akıllı Giriş Stratejisi Modülü
- **Dosya**: `smart_entry_strategy.py`
- **Amaç**: FVG+Fibonacci hibrit giriş stratejisi ve stop loss/take profit hesaplaması
- **Temel Sınıf**: `SmartEntryStrategy`
- **Temel Fonksiyonlar**:
  - `calculate_entry_levels()`: Ana giriş seviyesi hesaplama fonksiyonu
  - `_calculate_fvg_fibonacci_entry()`: FVG+Fibonacci hibrit giriş stratejisi **Yeni!**
  - `_calculate_fibonacci_entry()`: Normal Fibonacci giriş stratejisi
  - `_calculate_sl_tp()`: Stop loss ve take profit hesaplaması
  - `_calculate_tp_levels()`: TP1, TP2, TP3 seviyelerini hesaplar

### 18. Ana Koordinasyon Modülü
- **Dosya**: `main.py`
- **Amaç**: Tüm sistemi koordine eder ve komut satırı arayüzü sağlar
- **Temel Fonksiyonlar**:
  - `analyze_symbol()`: Belirli bir sembol ve zaman dilimi için tüm analizleri gerçekleştirir
  - `calculate_stats()`: Fiyat, VWAP, EMA ve diğer istatistikleri hesaplar
  - `format_volume()`: Hacim değerlerini okunaklı formata dönüştürür
  - `run_periodic_analysis()`: Periyodik analiz döngülerini başlatır
  - `log_daily_bias_report()`: Daily Bias analizlerini loglar
  - `log_supertrend_report()`: SuperTrend analizlerini loglar

## Veri Akışı

1. `main.py`, `bybit_client.py` aracılığıyla Bybit'ten verileri çeker
2. Veriler `pivot_analyzer.py`'ye iletilir ve pivot noktaları tespit edilir
3. Pivot noktaları kullanılarak swing patternleri tanımlanır
4. Bu swing patternleri, `patterns.py`'deki tanımlı formasyonlarla eşleştirilir
5. `divergence_analyzer.py` kullanılarak fiyat ve göstergeler arasındaki uyumsuzluklar tespit edilir
6. `fvrp_analyzer.py` modülü ile hacim profili ve POC, VAH, VAL değerleri hesaplanır
7. `npoc_analyzer.py` modülü ile naked POC noktaları tespit edilir
8. `fibonacci_analyzer.py` modülü ile Fibonacci seviyeleri ve altın bölgeler hesaplanır
9. `fvg_analyzer.py` modülü ile Fair Value Gap'ler tespit edilir **Yeni!**
10. `order_block_analyzer.py` modülü ile order block'lar tespit edilir (12 saatlik timeframe'de)
11. `bos_analyzer.py` modülü ile Break of Structure desenleri tespit edilir (son 3 pivot noktası kullanılarak)
12. `sfp_analyzer.py` modülü ile Swing Failure Pattern'lar tespit edilir
13. `timeframe_levels_analyzer.py` modülü ile zaman dilimi bazlı seviyeler hesaplanır
14. `daily_bias_analyzer.py` modülü ile günlük piyasa yönü belirlenir
15. `supertrend_analyzer.py` modülü ile SuperTrend hesaplaması ve analizi yapılır
16. `premium_discount_analyzer.py` modülü ile Premium/Discount bölge analizi yapılır **Yeni!**
17. `scoring_system.py` modülü ile tüm analizlerden elde edilen veriler puanlanır
18. `smart_entry_strategy.py` modülü ile FVG+Fibonacci hibrit giriş stratejisi hesaplanır **Yeni!**
19. `calculate_stats()` fonksiyonu ile önemli finansal metrikler hesaplanır
20. Sonuçlar sadeleştirilmiş log formatında loglanır **Yeni!**

## Yeni Özellikler (v1.3)

### FVG+Fibonacci Hibrit Giriş Stratejisi

Impulsive hareketlerde (rally-base-rally, drop-base-drop) daha etkili giriş noktaları sağlayan yeni hibrit strateji:

1. **FVG Tespiti**: Fiyata en yakın, doldurulmamış Fair Value Gap bölgesi bulunur
2. **Fibonacci Kombinasyonu**: FVG bölgesine en yakın Fibonacci seviyesi tespit edilir
3. **Hibrit Hesaplama**: FVG EQ seviyesi (%60) + Fibonacci seviyesi (%40) ağırlıklı ortalaması
4. **Bölge İçi Optimizasyon**: Trade direction'a göre FVG bölgesinin optimal kısmı kullanılır
5. **Mesafe Kontrolü**: %5'ten uzak girişler reddedilir, normal Fibonacci stratejisine geçilir

**Avantajları:**
- BNB örneğinde görüldüğü gibi, fiyat 0.5 seviyesine geri çekilmediğinde bile giriş fırsatı yakalar
- Impulsive hareketlerde daha erken ve etkili giriş noktaları sağlar
- FVG EQ seviyeleri ile Fibonacci seviyelerinin sinerjisini kullanır

### Stop Loss Hesaplama İyileştirmesi

Stop loss hesaplama mantığı, daha tutarlı ve öngörülebilir sonuçlar için güncellenmiştir:

1. **4 Saatlik Swing Noktaları**: Stop loss hesaplaması artık günlük (D) zaman dilimi yerine 4 saatlik (4h) zaman dilimindeki swing noktalarını kullanır.
2. **Sabit Likidite Payı**: Volatilite seviyesine göre dinamik ayarlama yerine, sabit %1 likidite payı kullanılır.
3. **Pivot Tabanlı Stop Loss**: İşlem yönüne uygun en son pivot noktası (BULL için düşük pivot, BEAR için yüksek pivot) + %1 likidite payı.
4. **Triple Pattern Stop Loss**: TRIT ve TRIB patternleri için sabit %2.5 stop loss kullanılır.
5. **Varsayılan Stop Loss**: Pivot bulunamadığında, giriş fiyatının %2.5 altında/üstünde stop loss belirlenir.

Bu değişiklikler, stop loss seviyelerinin daha yakın ve daha güncel pivot noktalarına dayanmasını sağlar, böylece stop loss mesafesi daha dar ve daha doğru olur.

### Sadeleştirilmiş Loglama Sistemi

Kritik bilgilere odaklanan, gereksiz detayları kaldıran temiz ve okunabilir log sistemi:

1. **Kısa Prefix'ler**: `[SL]`, `[TP]`, `[FVG]` gibi kategorize edilmiş loglar
2. **Emoji Göstergeleri**: `✅` başarı, `❌` hata durumları için görsel ipuçları
3. **Debug/Info Ayrımı**: Detaylı bilgiler DEBUG, kritik bilgiler INFO seviyesinde
4. **Temiz Konsol**: Gereksiz açıklamalar kaldırıldı, sadece önemli bilgiler gösteriliyor

**Örnek Log Karşılaştırması:**

**Önceki (Detaylı):**
```
[ETHUSDT] Volatilite seviyesi: MEDIUM (ATR: %2.15)
[ETHUSDT] FVG verileri mevcut (3 FVG), impulsive hareket kontrolü yapılıyor...
[ETHUSDT] En yakın bullish FVG bulundu: EQ=2461.32 (Mesafe: %1.25)
Stop Loss hesaplama başladı:
  Entry Price: 2461.32
  Trade Direction: bull
  Pattern Name: PHL3
```

**Yeni (Sadeleştirilmiş):**
```
[ETHUSDT] ✅ FVG+Fib giriş: 2461.32
[SL] ✅ MSB SL: 2450.15
```

## Yeni Özellikler (v1.2)

### Break of Structure (BOS) Tespiti

BOS tespiti, piyasa yapısındaki önemli kırılmaları tespit eder:

1. **Bullish BOS**: LH → LL → HH dizilimi (aşağı trendin kırılıp yukarı trende dönüşme sinyali)
2. **Bearish BOS**: HL → HH → LL dizilimi (yukarı trendin kırılıp aşağı trende dönüşme sinyali)
3. BOS tespiti sadece son 3 swing noktası kullanılarak yapılır
4. 1 saatlik (60) timeframe'de BOS analizi yapılmaz

### Order Block (OB) Tespiti

Order Block tespiti, potansiyel destrek/direnç bölgelerini tespit eder:

1. **Bullish OB**: Önemli yükseliş hareketinden önce oluşan son düşüş mumu
2. **Bearish OB**: Önemli düşüş hareketinden önce oluşan son yükseliş mumu
3. OB tespiti sadece 12 saatlik (720) timeframe'de yapılır

### SuperTrend Analizi

SuperTrend göstergesi, trend yönünü belirlemek için geliştirilmiş bir teknik analiz aracıdır. Ana özellikler:

- ATR (Average True Range) hesaplamasına dayanır
- Üst ve alt bantları hesaplayarak trend yönünü belirler
- ATR Periyodu ve Çarpanı ayarlanabilir (Varsayılan: 10 periyot ve 3.0 çarpan)
- Mevcut fiyat ile SuperTrend çizgisi arasındaki uzaklık (mutlak ve yüzde olarak)

SuperTrend göstergesi, puanlama sistemine +1 (UP) veya -1 (DOWN) katkı sağlar. Ayrıca, UP/DOWN durumu Daily Bias ile uyumlu olduğunda ek puan alır.

### Daily Bias Analizi

Daily Bias analizi, günlük grafiklerde piyasa yönünü belirlemek için kullanılır:

1. **Bullish Bias**: Yükseliş trendini gösteren pozitif bias (1)
2. **Bearish Bias**: Düşüş trendini gösteren negatif bias (-1)
3. **Nötr Bias**: Belirgin bir yön göstermeyen durum (0)
4. **Analiz Faktörleri**:
   - EMA konumları (26, 50, 100)
   - Kapanış fiyatının EMA'lara göre pozisyonu
   - Mum formasyonları ve fiyat hareketi kalıpları
   - Hacim trendleri ve hacim/fiyat ilişkisi
5. **Sistem Entegrasyonu**:
   - Günlük bazda her sembol için bias değeri hesaplanır
   - Puanlama sistemine entegre edilmiştir (Bullish: +1, Bearish: -1, Nötr: 0)
   - Diğer zaman dilimi analizlerinde referans olarak kullanılır
   - Merkezi loglama sistemine entegre edilmiştir
6. Analiz, günlük grafikler üzerinde gerçekleştirilir ve diğer timeframe'ler için referans sağlar

### Puanlama Sistemi İyileştirmeleri

Puanlama sistemi şu bileşenlerden oluşur:

- Her tespit edilen formasyon için +3 puan (boğa veya ayı)
- Her divergence tespiti için +1 puan
- Her zaman dilimi seviyesi için +0.5 puan
- Daily Bias puanları (diğer analizlerle uyum durumunda)
- SuperTrend puanları (diğer analizlerle uyum durumunda)
- Fibonacci Golden Zone puanları

Ayrıca:

- Merkezi log sistemi tüm analiz sonuçlarını standart formatta kaydeder
- Telegram entegrasyonu ile gerçek zamanlı bildirimler gönderilir
- Kapsamlı raporlama sistemi ile puanlama sonuçları gösterilir

## Kurulum

1. Gerekli paketleri yükleyin:
   ```bash
   pip install -r requirements.txt
   ```

2. `.env` dosyasını oluşturun ve aşağıdaki değişkenleri ayarlayın:
   ```
   SYMBOLS=BTCUSDT,ETHUSDT,SOLUSDT,AVAXUSDT,XRPUSDT,SUIUSDT
   TIMEFRAMES=240,720,D
   MAX_CANDLES=230
   ANALYSIS_INTERVAL_SECONDS=180
   NPOC_SFP_UPDATE_INTERVAL_SECONDS=3600
   LOG_DIRECTORY=./logs
   ```

## Kullanım

### Konsol Uygulaması

Ana analiz modülünü çalıştırın:
```bash
python main.py
```

### Web Arayüzü

İnteraktif grafik arayüzünü başlatın:
```bash
python web_app.py
```
Web tarayıcınızda `http://localhost:5041` adresine giderek arayüze erişebilirsiniz.

## Konfigürasyon

`.env` dosyasında aşağıdaki parametreleri özelleştirebilirsiniz:

- `SYMBOLS`: Analiz edilecek kripto para sembolleri (virgülle ayrılmış)
- `TIMEFRAMES`: Analiz edilecek zaman dilimleri (virgülle ayrılmış, 240 = 4s, 720 = 12s, "D" günlük)
- `MAX_CANDLES`: Çekilecek maksimum mum sayısı
- `ANALYSIS_INTERVAL_SECONDS`: Analizlerin güncellenme sıklığı (saniye)
- `NPOC_SFP_UPDATE_INTERVAL_SECONDS`: NPOC ve SFP analizlerinin güncellenme sıklığı (saniye)

## Log Dosyaları

Analiz sonuçları aşağıdaki log dosyalarında saklanır:

- `logs/app_{zaman}.log`: Ana uygulama logları
- `logs/sfp_analysis.log`: SFP analiz sonuçları
- `logs/npoc_summary.log`: Naked POC analiz özeti
- `logs/scoring_report_combined.log`: Puanlama sistemi birleştirilmiş raporu (önemli sinyaller)

## Notlar

- Analiz sonuçları gerçek zamanlı olarak konsolda gösterilir.
- Web arayüzü canlı olarak güncellenebilir grafik ve tablolar sunar.
- 1 saatlik (60) timeframe artık analizlerde kullanılmamaktadır.