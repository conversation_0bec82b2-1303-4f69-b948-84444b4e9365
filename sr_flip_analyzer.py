from typing import Dict, List, Any, Optional
from loguru import logger

class SRFlipAnalyzer:
    """
    Destek/Direnç dö<PERSON>ümlerini (S/R Flip) analiz eder.
    
    S/R Flip, piyasanın "hafızasını" ve önceki engellerin artık birer basamak 
    olduğunu gösteren güçlü bir teyit mekanizmasıdır.
    
    Mantık:
    - Bullish S/R Flip: Önceden direnç olan seviyenin kırıldıktan sonra destek olarak test edilmesi
    - Bearish S/R Flip: Önceden destek olan seviyenin kırıldıktan sonra direnç olarak test edilmesi
    """
    
    def __init__(self, tolerance_pct: float = 0.005):
        """
        SRFlipAnalyzer sınıfını başlatır.
        
        Args:
            tolerance_pct (float): Bir seviyenin test edildiğini kabul etmek için yüzde tolerans.
                                 Varsayılan %0.5 (0.005)
        """
        self.tolerance_pct = tolerance_pct
        logger.info(f"S/R Flip Analyzer başlatıldı. Tolerans: %{tolerance_pct*100:.1f}")

    def analyze(self, last_price: float, pivots: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[float]]:
        """
        Pivot yapısını kullanarak S/R Flip olaylarını tespit eder.
        
        Bu fonksiyon günlük (Daily) zaman dilimi pivot verilerini kullanarak
        güçlü S/R flip seviyelerini tespit eder.
        
        Args:
            last_price (float): Mevcut son fiyat
            pivots (dict): 'high_pivots' ve 'low_pivots' listelerini içeren sözlük
            
        Returns:
            dict: {'bullish_flips': [fiyat_seviyesi], 'bearish_flips': [fiyat_seviyesi]}
        """
        high_pivots = pivots.get('high_pivots', [])
        low_pivots = pivots.get('low_pivots', [])

        if not high_pivots or not low_pivots:
            logger.debug("S/R Flip analizi için yeterli pivot verisi yok")
            return {'bullish_flips': [], 'bearish_flips': []}

        bullish_flips = []
        bearish_flips = []

        # Bullish Flip Tespiti (Eski Direnç -> Yeni Destek)
        bullish_flips = self._detect_bullish_flips(high_pivots, last_price)
        
        # Bearish Flip Tespiti (Eski Destek -> Yeni Direnç)  
        bearish_flips = self._detect_bearish_flips(low_pivots, last_price)

        # Sonuçları logla
        if bullish_flips:
            logger.info(f"💎 Bullish S/R Flip tespit edildi: {len(bullish_flips)} seviye")
            for level in bullish_flips:
                logger.debug(f"  └─ Bullish Flip seviyesi: {level:.4f}")
                
        if bearish_flips:
            logger.info(f"💎 Bearish S/R Flip tespit edildi: {len(bearish_flips)} seviye")
            for level in bearish_flips:
                logger.debug(f"  └─ Bearish Flip seviyesi: {level:.4f}")

        return {
            'bullish_flips': list(set(bullish_flips)), 
            'bearish_flips': list(set(bearish_flips))
        }

    def _detect_bullish_flips(self, high_pivots: List[Dict[str, Any]], last_price: float) -> List[float]:
        """
        Bullish S/R Flip seviyelerini tespit eder.
        
        Mantık: Önceki direnç seviyesi (high pivot) kırıldıktan sonra,
        fiyatın geri çekilip aynı seviyeyi destek olarak test etmesi.
        
        Args:
            high_pivots: Yüksek pivot noktaları listesi
            last_price: Mevcut fiyat
            
        Returns:
            List[float]: Bullish flip seviyelerinin listesi
        """
        bullish_flips = []
        
        # Son 5 yüksek pivotu potansiyel flip seviyesi olarak değerlendir
        for i, r_pivot in enumerate(high_pivots[-5:]):
            r_level = float(r_pivot['price'])
            
            # Bu pivottan sonraki pivotları kontrol et
            future_pivots = high_pivots[len(high_pivots) - 5 + i + 1:]
            
            # Kırılım teyidi: Bu pivottan daha yüksek bir pivot oluşmuş mu?
            break_confirmed = any(float(future_p['price']) > r_level for future_p in future_pivots)

            # Test teyidi: Mevcut fiyat bu seviyeyi destek olarak test ediyor mu?
            price_distance = abs(last_price - r_level) / r_level
            test_confirmed = (price_distance < self.tolerance_pct and last_price >= r_level)
            
            if break_confirmed and test_confirmed:
                bullish_flips.append(r_level)
                logger.trace(f"Bullish S/R Flip tespit edildi: {r_level:.4f} (Mesafe: %{price_distance*100:.2f})")

        return bullish_flips

    def _detect_bearish_flips(self, low_pivots: List[Dict[str, Any]], last_price: float) -> List[float]:
        """
        Bearish S/R Flip seviyelerini tespit eder.
        
        Mantık: Önceki destek seviyesi (low pivot) kırıldıktan sonra,
        fiyatın geri yükselip aynı seviyeyi direnç olarak test etmesi.
        
        Args:
            low_pivots: Düşük pivot noktaları listesi
            last_price: Mevcut fiyat
            
        Returns:
            List[float]: Bearish flip seviyelerinin listesi
        """
        bearish_flips = []
        
        # Son 5 düşük pivotu potansiyel flip seviyesi olarak değerlendir
        for i, s_pivot in enumerate(low_pivots[-5:]):
            s_level = float(s_pivot['price'])

            # Bu pivottan sonraki pivotları kontrol et
            future_pivots = low_pivots[len(low_pivots) - 5 + i + 1:]
            
            # Kırılım teyidi: Bu pivottan daha düşük bir pivot oluşmuş mu?
            break_confirmed = any(float(future_p['price']) < s_level for future_p in future_pivots)
            
            # Test teyidi: Mevcut fiyat bu seviyeyi direnç olarak test ediyor mu?
            price_distance = abs(last_price - s_level) / s_level
            test_confirmed = (price_distance < self.tolerance_pct and last_price <= s_level)

            if break_confirmed and test_confirmed:
                bearish_flips.append(s_level)
                logger.trace(f"Bearish S/R Flip tespit edildi: {s_level:.4f} (Mesafe: %{price_distance*100:.2f})")

        return bearish_flips
