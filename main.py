"""
Ana uygulama modülü.
Bybit API'ye ba<PERSON><PERSON><PERSON>r, verileri çeker ve analiz eder.
"""

import os
import sys
import time
import traceback
import collections
from datetime import datetime, timedelta
import pandas as pd
from dotenv import load_dotenv
from loguru import logger
from typing import Dict, List, Optional, Tuple, Any
import re
import numpy as np

from pivot_analyzer import PivotAnalyzer
from bybit_client import BybitClient
from patterns import BULL_PATTERNS, BEAR_PATTERNS
from divergence_analyzer import DivergenceAnalyzer
from fvrp_analyzer import analyze_historical_fvrp, FVRPAnalyzer
from npoc_analyzer import NPOCAnalyzer
from timeframe_levels_analyzer import TimeframeLevelsAnalyzer
try:
    from sfp_analyzer import SFPAnalyzer
except ImportError:
    SFPAnalyzer = None
    logger.warning("SFPAnalyzer modülü bulunamadı.")
from vwap_calculator import calculate_vwap
from fibonacci_analyzer import FibonacciAnalyzer
from order_block_analyzer import OrderBlockAnalyzer
from bos_analyzer import BOSAnalyzer
from scoring_system import ScoringSystem
from utils import format_price_standard, format_volume, shorten_indicator_names
from daily_bias_analyzer import DailyBiasAnalyzer
from supertrend_analyzer import SuperTrendAnalyzer
from stats_tracker import StatsTracker
from config_manager import load_config
from notification_service import NotificationService
from stats_reporter import StatsReporter
from fvg_analyzer import FVGAnalyzer
from smart_entry_strategy import SmartEntryStrategy
from sr_flip_analyzer import SRFlipAnalyzer

# .env dosyasını yükle
load_dotenv()

# Logs klasörünü oluştur (yoksa)
os.makedirs("logs", exist_ok=True)

# Log konfigürasyonu - ESKİ FORMATA ÇEVİRİLDİ
logger.remove()
# Konsol için sadece INFO seviyesinde ve temiz format
logger.add(
    sys.stderr,
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
)
# Dosya için bilgi ve daha üst seviyede ama daha temiz format
logger.add(
    "logs/app_{time:YYYY-MM-DD_HH-mm-ss_SSSSSS}.log",
    rotation="1 day",
    retention="7 days",
    level="INFO",  # DEBUG yerine INFO kullan
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    encoding='utf-8'
)

# Maksimum tampon boyutu
MAX_BUFFER_SIZE = 15  # Son 15 swing noktasını saklayacağız

# Düzeltme: En son başarılı SFP listesini saklamak için global değişken
last_successful_sfps: List[Dict] = []

# Günlük analiz sonuçlarını saklamak için global değişken
daily_analysis_results: Dict[str, Dict[str, Any]] = {}

def get_expected_interval(timeframe: str) -> pd.Timedelta:
    """
    Verilen zaman dilimi string'ine göre beklenen mum süresini (interval) hesaplar.

    Args:
        timeframe (str): Zaman dilimi string'i (örn. "60", "240", "D").

    Returns:
        pd.Timedelta: Hesaplanan zaman aralığı. 'D' için 1 gün, diğerleri için dakika.
                      Geçersiz timeframe girilirse ValueError verebilir.
    """
    if timeframe.upper() == "D":
        return pd.Timedelta(days=1)
    elif timeframe.isdigit():
        return pd.Timedelta(minutes=int(timeframe))
    else:
        logger.warning(f"Beklenmeyen zaman dilimi formatı: {timeframe}. Varsayılan 60 dakika kullanılıyor.")
        return pd.Timedelta(minutes=60) # Hata durumu için varsayılan

# Verileri çek ve analiz et
def analyze_symbol(
    symbol: str,
    timeframe: str,
    max_candles: int = 200,
    all_patterns: Optional[Dict[str, List[Dict[str, str]]]] = None,
    all_divergences: Optional[Dict[str, List[Dict[str, Any]]]] = None,
    all_bos_results: Optional[Dict[str, Dict[str, Any]]] = None,
    all_timeframe_data: Optional[Dict[str, Dict[str, Dict[str, Any]]]] = None
) -> Dict[str, Any]:
    """
    Belirtilen sembol ve zaman dilimi için Bybit'ten veri çeker, çeşitli analizler
    (pivot, swing, divergence, VWAP, FVRP vb.) yapar ve sonuçları loglar/saklar.

    Args:
        symbol (str): Analiz edilecek kripto para sembolü (örn. "BTCUSDT").
        timeframe (str): Mum zaman dilimi (örn. "30", "60", "240", "D").
        max_candles (int): Bybit'ten çekilecek maksimum mum sayısı. Varsayılan: 200.
        all_patterns (Optional[Dict]): Eğer verilirse, tespit edilen pattern'ler
                                       bu sözlüğe `{symbol}_{timeframe}` anahtarı ile eklenir.
        all_divergences (Optional[Dict]): Eğer verilirse, tespit edilen divergence'lar
                                          bu sözlüğe `{symbol}_{timeframe}` anahtarı ile eklenir.
        all_bos_results (Optional[Dict]): Eğer verilirse, BOS analiz sonuçları bu sözlüğe `{symbol}_{timeframe}` anahtarı ile eklenir.

    Returns:
        Dict[str, Any]: Analiz sonuçlarını içeren bir sözlük. İçeriği:
                        - "patterns": Tespit edilen pattern listesi.
                        - "funding_rate": Güncel funding oranı (varsa).
                        - "price_change_24h": Son 24 saatlik fiyat değişimi yüzdesi.
                        - "volume_24h": Son 24 saatlik hacim (coin cinsinden).
                        - "turnover_24h": Son 24 saatlik ciro (USDT cinsinden).
                        - "vwap": Son hesaplanan günlük VWAP değeri.
                        - "divergences": Tespit edilen divergence listesi.
                        - "swing_points": Tespit edilen swing noktaları listesi.
                        - "candles": Kullanılan mum verisi DataFrame'i.
                        - "bos_results": Tespit edilen BOS sonuçları.
                        - "supertrend": SuperTrend analiz sonuçları.
                        - "fib_levels": Fibonacci seviyeleri (sadece D).
                        - "fvgs": FVG analiz sonuçları (sadece D).
    """
    result: Dict[str, Any] = {
        "patterns": [],
        "funding_rate": None,
        "price_change_24h": None,
        "volume_24h": None,
        "turnover_24h": None,
        "vwap": None,
        "divergences": [],
        "swing_points": [],
        "candles": None,
        "bos_results": {},
        "daily_bias": 0, # Varsayılan olarak 0 (Nötr) ata
        "supertrend": None
    }

    # Varsayılan değerlerle EMA değişkenlerini önceden tanımla
    ema26_str = "N/A"
    ema26_diff_str = "N/A"
    ema50_str = "N/A"
    ema50_diff_str = "N/A"
    ema100_str = "N/A"
    ema100_diff_str = "N/A"

    try:
        # Bybit bağlantısı
        client = BybitClient()

        # Verileri çek
        candles: Optional[pd.DataFrame] = client.fetch_klines(symbol, timeframe, max_candles)

        if candles is None or candles.empty or len(candles) < 10:
            logger.warning(f"{symbol} {timeframe} zaman dilimi için yeterli veri bulunamadı veya çekilemedi!")
            # Ekstra: Eğer timeframe 'D' ise, daha belirgin log ekle
            if timeframe == 'D':
                logger.warning(f"!!! {symbol} için GÜNLÜK (D) zaman dilimi verisi çekilemedi. all_timeframe_data'ya eklenmeyecek.")
            return result

        result["candles"] = candles # Candles'ı sonuca ekle

        # Ticker verilerini çek (24 saatlik fiyat ve hacim değişimi)
        ticker_data: Optional[Dict[str, Any]] = client.fetch_ticker(symbol)

        # PivotAnalyzer oluştur (değişiklik burada)
        analyzer = PivotAnalyzer(timeframe=timeframe)

        # Pivot ve Swing analizi
        df_with_pivots: pd.DataFrame = analyzer.find_pivots(candles)
        df_with_swings, swing_points = analyzer.identify_swings(df_with_pivots)

        # Swing noktalarını all_timeframe_data yapısına ekle
        if all_timeframe_data is not None:
            if symbol not in all_timeframe_data:
                all_timeframe_data[symbol] = {}
            if timeframe not in all_timeframe_data[symbol]:
                all_timeframe_data[symbol][timeframe] = {}

            all_timeframe_data[symbol][timeframe]['swing_points'] = swing_points
            logger.debug(f"{symbol}/{timeframe} için all_timeframe_data yapısına {len(swing_points)} swing noktası eklendi.")

        # Divergence analizi için göstergeleri hesapla
        divergence_analyzer = DivergenceAnalyzer()
        df_with_indicators: pd.DataFrame = divergence_analyzer.calculate_indicators(candles)
        divergences: List[Dict[str, Any]] = divergence_analyzer.detect_hidden_divergences(
            df_with_indicators,
            swing_points,
            max_points=10, # Kontrol edilecek maks swing sayısı
            max_bars=100,  # Swingler arası maks mum sayısı
            timeframe=timeframe  # Zaman dilimini geçir
        )

        # Divergence'ları logla
        for div in divergences:
            div_indicators: List[str] = div.get('indicators', [])
            shortened_indicators: str = shorten_indicator_names(div_indicators)
            indicator_count: int = len(div_indicators)
            # div['type'] 'Positive Hidden Bullish' veya 'Negative Hidden Bearish' olabilir
            div_type_str: str = div.get('type', '')
            div_label: str = "BULL" if "Positive" in div_type_str else ("BEAR" if "Negative" in div_type_str else "??")
            pivot_time_dt: Optional[datetime] = div.get('pivot_time')
            pivot_time_str: str = pivot_time_dt.strftime('%d/%m/%Y %H:%M') if pivot_time_dt else "N/A"
            logger.info(f"{symbol} {timeframe} | {div_label} | '{indicator_count}' | {shortened_indicators} | Pivot Zamanı: {pivot_time_str}")

        # Son EMA değerlerini al (hata kontrolü ile)
        last_ema26 = df_with_indicators['ema26'].iloc[-1] if 'ema26' in df_with_indicators.columns and not df_with_indicators['ema26'].empty else None
        last_ema50 = df_with_indicators['ema50'].iloc[-1] if 'ema50' in df_with_indicators.columns and not df_with_indicators['ema50'].empty else None
        last_ema100 = df_with_indicators['ema100'].iloc[-1] if 'ema100' in df_with_indicators.columns and not df_with_indicators['ema100'].empty else None

        last_price: Optional[float] = None
        if ticker_data:
            result["price_change_24h"] = ticker_data.get("price_change_24h")  # Yüzde değeri
            result["volume_24h"] = ticker_data.get("volume_24h")  # Coin miktarı
            result["turnover_24h"] = ticker_data.get("turnover_24h")  # USDT miktarı
            last_price = ticker_data.get("last_price")
        else:
            # Ticker verisi yoksa mumlardan son fiyatı al
            if not candles['close'].empty:
                last_price = candles['close'].iloc[-1]

        # VWAP hesapla (YENİ MODÜLDEN)
        # Not: anchor='D' günlük periyot anlamına gelir (Pandas offset alias)
        vwap_series: Optional[pd.Series] = calculate_vwap(candles, anchor="D")
        last_vwap: Optional[float] = None
        vwap_diff_pct: Optional[float] = None
        if vwap_series is not None and not vwap_series.empty:
            last_vwap = vwap_series.iloc[-1]
            result["vwap"] = last_vwap
            # VWAP ile son fiyat arasındaki farkı yüzde olarak hesapla (eğer her ikisi de varsa)
            if last_price is not None and last_vwap is not None and last_vwap != 0:
                vwap_diff_pct = ((last_price - last_vwap) / last_vwap) * 100

        # FVRP (Fixed Range Volume Profile) hesapla (son periyot için)
        # Not: FVRPAnalyzer instance'ı globalde tanımlı
        volume_profile: Dict[str, Optional[float]] = fvrp_analyzer.calculate_volume_profile(
            candles, resolution=30, va_width_pct=70
        )
        poc_price: Optional[float] = volume_profile.get('poc')
        vah_price: Optional[float] = volume_profile.get('vah')
        val_price: Optional[float] = volume_profile.get('val')

        # FVRP fiyat farkları hesaplama
        if last_price is not None:
            if poc_price is not None and poc_price != 0:
                poc_diff_pct = ((last_price - poc_price) / poc_price) * 100
            if vah_price is not None and vah_price != 0:
                vah_diff_pct = ((last_price - vah_price) / vah_price) * 100
            if val_price is not None and val_price != 0:
                val_diff_pct = ((last_price - val_price) / val_price) * 100

        # Funding oranını çek
        try:
            funding_rate: Optional[float] = client.fetch_funding_rate(symbol)
            result["funding_rate"] = funding_rate
        except Exception as e:
            funding_rate = None # Hata durumunda None ata
            logger.warning(f"{symbol} için funding oranı çekilemedi: {e}")

        # EMA String ve Farkları
        if last_price is not None:
            if last_ema26 is not None and last_ema26 != 0:
                ema26_diff = ((last_price - last_ema26) / last_ema26) * 100
                ema26_str = f"{last_ema26:.4f}"
                ema26_diff_str = f"{ema26_diff:+.2f}%"
            if last_ema50 is not None and last_ema50 != 0:
                ema50_diff = ((last_price - last_ema50) / last_ema50) * 100
                ema50_str = f"{last_ema50:.4f}"
                ema50_diff_str = f"{ema50_diff:+.2f}%"
            if last_ema100 is not None and last_ema100 != 0:
                ema100_diff = ((last_price - last_ema100) / last_ema100) * 100
                ema100_str = f"{last_ema100:.4f}"
                ema100_diff_str = f"{ema100_diff:+.2f}%"

        last_price_str = format_price_standard(last_price)

        # --- Order Block Analizi (12h ve 4h için) ---
        ob_bull_log = ""
        ob_bear_log = ""
        # Tanımlı OB değişkenlerini burada kullanalım (döngü içinden)
        bull_ob_data_for_log = None
        bear_ob_data_for_log = None
        if timeframe == '720' or timeframe == '240':
            try:
                latest_obs = order_block_analyzer.find_latest_order_blocks(candles)
                bull_ob_data_for_log = latest_obs.get('bullish') # Bu değişkeni loglamada kullan
                bear_ob_data_for_log = latest_obs.get('bearish') # Bu değişkeni loglamada kullan
            except Exception as ob_e:
                logger.error(f"{symbol} {timeframe} Order Block analizi hatası: {ob_e}", exc_info=False)

        # --- BOS Analizi ---
        bos_results_current = bos_analyzer.analyze_symbol(symbol, timeframe, swing_points)
        result["bos_results"] = bos_results_current
        if all_bos_results is not None:
            all_bos_results[f"{symbol}_{timeframe}"] = bos_results_current

        # --- SuperTrend Analizi ---
        try:
            supertrend_results = supertrend_analyzer.analyze_candles(symbol, timeframe, candles)
            result["supertrend"] = supertrend_results

            # SuperTrend bilgileri değişkenlere alınıyor, ama loglama ana loglama bölümüne taşıyacağız
            if supertrend_results:
                st_trend = supertrend_results.get('trend', 'bilinmiyor')
                st_value = supertrend_results.get('formatted_supertrend', format_price_standard(supertrend_results.get('supertrend_value', 0)))
                st_distance = supertrend_results.get('formatted_distance', format_price_standard(supertrend_results.get('distance', 0)))
                st_distance_pct = supertrend_results.get('distance_percent', 0)

                # Buradaki direkt loglama kaldırıldı - ana log içine taşınacak
        except Exception as st_error:
            logger.error(f"{symbol} {timeframe} SuperTrend analizi hatası: {st_error}", exc_info=True)
            # Hata durumunda varsayılan değerler
            st_trend = "bilinmiyor"
            st_value = "N/A"
            st_distance = "N/A"
            st_distance_pct = 0

        # --- Toplu Loglama (Standart Formatla) ---
        # ... (Fiyat, EMA, POC, VAH, VAL, VWAP formatlama) ...

        # --- OB ve BOS logları için doğru değişkenleri kullan ---
        ob_bull_log = ""
        # DÜZELTME: bull_ob yerine bull_ob_data_for_log kullan
        if bull_ob_data_for_log and last_price is not None:
            ob_price = bull_ob_data_for_log.get('price')
            if ob_price is not None:
                diff_pct = ((last_price - ob_price) / ob_price) * 100 if ob_price != 0 else 0
                ob_bull_log = f" | Bullish OB: {format_price_standard(ob_price)} ({diff_pct:+.1f}%)"

        ob_bear_log = ""
        # DÜZELTME: bear_ob yerine bear_ob_data_for_log kullan
        if bear_ob_data_for_log and last_price is not None:
            ob_price = bear_ob_data_for_log.get('price')
            if ob_price is not None:
                diff_pct = ((last_price - ob_price) / ob_price) * 100 if ob_price != 0 else 0
                ob_bear_log = f" | Bearish OB: {format_price_standard(ob_price)} ({diff_pct:+.1f}%)"

        bos_bull_log = ""
        # DÜZELTME: current_bos_data yerine bos_results_current kullan
        if bos_results_current.get('bullish') and last_price is not None:
            bos_price = bos_results_current['bullish'].get('price')
            if bos_price is not None:
                 curr_diff_pct = ((last_price - bos_price) / bos_price) * 100 if bos_price != 0 else 0
                 bos_time = bos_results_current['bullish'].get('timestamp') # time_str buradan hesaplanmalı
                 time_str = bos_time.strftime('%d-%m %H:%M') if pd.notna(bos_time) else 'N/A'
                 bos_bull_log = f" | Bull BOS: {format_price_standard(bos_price)} ({curr_diff_pct:+.1f}%) | {time_str}"

        bos_bear_log = ""
        # DÜZELTME: current_bos_data yerine bos_results_current kullan
        if bos_results_current.get('bearish') and last_price is not None:
            bos_price = bos_results_current['bearish'].get('price')
            if bos_price is not None:
                 curr_diff_pct = ((last_price - bos_price) / bos_price) * 100 if bos_price != 0 else 0
                 bos_time = bos_results_current['bearish'].get('timestamp') # time_str buradan hesaplanmalı
                 time_str = bos_time.strftime('%d-%m %H:%M') if pd.notna(bos_time) else 'N/A'
                 bos_bear_log = f" | Bear BOS: {format_price_standard(bos_price)} ({curr_diff_pct:+.1f}%) | {time_str}"

        # SuperTrend log bilgileri
        st_log = ""
        if result.get("supertrend"):
            st_trend = result["supertrend"].get('trend', 'bilinmiyor').upper()
            st_value = result["supertrend"].get('formatted_supertrend', 'N/A')
            st_distance = result["supertrend"].get('formatted_distance', 'N/A')
            st_distance_pct = result["supertrend"].get('distance_percent', 0)
            st_log = f" | SuperTrend: {st_trend} | Değer: {st_value} | Uzaklık: {st_distance} ({st_distance_pct:.2f}%)"

        # --- Ana Log Mesajı ---
        logger.info(
            f"{symbol} {timeframe} | Fiyat: {last_price_str} | "
            f"EMA26: {ema26_str} ({ema26_diff_str}) | EMA50: {ema50_str} ({ema50_diff_str}) | EMA100: {ema100_str} ({ema100_diff_str}) | "
            f"POC: {format_price_standard(poc_price)} ({format_price_standard(poc_diff_pct) if poc_diff_pct is not None else 'N/A'}) | VAH: {format_price_standard(vah_price)} ({format_price_standard(vah_diff_pct) if vah_diff_pct is not None else 'N/A'}) | VAL: {format_price_standard(val_price)} ({format_price_standard(val_diff_pct) if val_diff_pct is not None else 'N/A'}) | VWAP: {format_price_standard(last_vwap)} ({format_price_standard(vwap_diff_pct) if vwap_diff_pct is not None else 'N/A'}) | "
            f"Değişim(24s): {result.get('price_change_24h', 0):.2f}% | Hacim: {format_volume(result.get('volume_24h', 0))} | Ciro: {format_volume(result.get('turnover_24h', 0))}$ | Funding: {(result.get('funding_rate', 0) or 0):.6f}%"
            f"{ob_bull_log}{ob_bear_log}{bos_bull_log}{bos_bear_log}{st_log}" # SuperTrend bilgileri eklendi
        )
        # --- Loglama Sonu ---

        # Verilerin güncelliğini kontrol et
        current_time_utc = pd.Timestamp.utcnow().tz_convert(None)
        last_candle_time = candles['timestamp'].iloc[-1]
        time_diff = current_time_utc - last_candle_time

        expected_interval = get_expected_interval(timeframe)
        if time_diff > expected_interval * 2:
            logger.warning(
                f"{symbol} verileri {time_diff} kadar eski! "
                f"Şu anki UTC: {current_time_utc.strftime('%d/%m/%Y %H:%M')}, "
                f"Son veri UTC: {last_candle_time.strftime('%d/%m/%Y %H:%M')}"
            )

        # Pattern tespiti yap
        detected_patterns = analyzer.detect_patterns(swing_points, timeframe=timeframe)
        result["patterns"] = detected_patterns

        # Tespit edilen divergence'ları sonuç sözlüğüne ekle (yukarıda hesaplandı)
        result["divergences"] = divergences

        # Eğer all_patterns/all_divergences sözlükleri verilmişse, sonuçları ekle
        if all_patterns is not None and detected_patterns:
            all_patterns[f"{symbol}_{timeframe}"] = detected_patterns
        if all_divergences is not None and divergences:
            all_divergences[f"{symbol}_{timeframe}"] = divergences

        # Son swing dizisini logla
        swings_with_type = [sp['type'] for sp in swing_points if sp.get('type')]
        last_n_swings = swings_with_type[-MAX_BUFFER_SIZE:] # Son N swing
        swing_sequence = ','.join(last_n_swings)
        logger.info(f"{symbol} {timeframe} | Son Swing Dizisi ({len(last_n_swings)}): {swing_sequence}")

        # Detaylı debug bilgisi
        logger.debug(f"{symbol} {timeframe} için toplam {len(swing_points)} swing noktası bulundu.")
        logger.debug(f"{symbol} {timeframe} için toplam {len(detected_patterns)} pattern tespit edildi.")

        # Fibonacci Analizi (SADECE GÜNLÜK timeframe için yapılmalı)
        # Bu hesaplama diğer zaman dilimleri için yapılmamalıdır
        fib_data = None

        if timeframe == 'D' and swing_points: # Sadece Günlük ve swing noktaları varsa
            try:
                fib_data = fib_analyzer.calculate_fibonacci_levels(swing_points)
                # Fibonacci sonuçlarını result sözlüğüne ekle (sadece Daily için)
                result["fib_levels"] = fib_data

                # Günlük sonuçları sakla
                if symbol not in daily_analysis_results:
                    daily_analysis_results[symbol] = {}
                daily_analysis_results[symbol]['fib_levels'] = fib_data

            except Exception as e:
                logger.error(f"{symbol} {timeframe} için Fibonacci hesaplama hatası: {e}")
        else:
            # D ve 4H dışındaki timeframe'ler için boş değer ata
            result["fib_levels"] = None

        # FVG Analizi (GÜNLÜK ve 4H timeframe için)
        fvgs_list = []
        if (timeframe == 'D' or timeframe == '240') and candles is not None and not candles.empty:
            try:
                fvgs_list = fvg_analyzer.find_fvgs(candles)
                result["fvgs"] = fvgs_list  # Sonuca ekle

                # Sonuçları sakla
                if symbol not in daily_analysis_results:
                    daily_analysis_results[symbol] = {}
                daily_analysis_results[symbol]['fvgs'] = fvgs_list

                logger.info(f"[{symbol} {timeframe}] {len(fvgs_list)} FVG bulundu.")
            except Exception as e:
                logger.error(f"[{symbol} {timeframe}] FVG analizi hatası: {e}", exc_info=True)
        else:
            # D ve 4H dışındaki timeframe'ler için boş değer ata
            result["fvgs"] = None

        # LOGLAMA BÖLÜMÜ
        # -----------------------------------------------
        log_prefix = f"[{symbol} {timeframe}]"
        # ... (mevcut loglamalar: swings, divergence, vwap, poc farkları) ...

        # Fibonacci Loglaması (Sadece 'D' timeframe için ve veri varsa)
        if timeframe == 'D' and fib_data:
            try:
                # Daha okunaklı bir log formatı oluşturalım
                start_type = fib_data['start_swing'].get('type', 'N/A')
                end_type = fib_data['end_swing'].get('type', 'N/A')
                trend = "Up" if fib_data['is_uptrend'] else "Down"
                fib_log_parts = [f"Fib({trend} {start_type}->{end_type})"]

                # Önemli seviyeleri ekle (varsa)
                eq_level = fib_data['levels'].get(0.5)
                if eq_level: fib_log_parts.append(f"EQ:{eq_level['price']:.4f}")

                ote_level = fib_data['levels'].get(0.75)
                if ote_level: fib_log_parts.append(f"OTE:{ote_level['price']:.4f}")

                # Altın Oran bölgelerini ekle (varsa)
                gs1 = fib_data['zones'].get('golden_spot1')
                if gs1: fib_log_parts.append(f"GS1:[{gs1['start']:.4f}-{gs1['end']:.4f}]")

                gs2 = fib_data['zones'].get('golden_spot2')
                if gs2: fib_log_parts.append(f"GS2:[{gs2['start']:.4f}-{gs2['end']:.4f}]")

                logger.info(f"{log_prefix} {' | '.join(fib_log_parts)}")
            except Exception as e:
                logger.error(f"{log_prefix} Fibonacci loglama hatası: {e}")

        # ... (SFP loglaması varsa) ...
        # -----------------------------------------------

        # ... (Fonksiyonun geri kalanı) ...

        # Fonksiyonun sonunda swing_points'i ve candles'ı da döndür
        result["swing_points"] = swing_points
        result["candles"] = candles # Hata olsa bile çekilen candles'ı döndürmeye çalışabiliriz

        # Daily Bias analizi (SADECE 'D' timeframe için HESAPLA)
        if timeframe == 'D':
            try:
                daily_bias_value = daily_bias_analyzer.determine_bias(symbol, timeframe, candles)
                result["daily_bias"] = daily_bias_value # Hesaplanan D bias'ını sakla
            except Exception as db_error:
                 logger.error(f"{symbol} {timeframe} Daily Bias HESAPLAMA hatası: {db_error}")
                 result["daily_bias"] = 0 # Hata durumunda nötr ata
        else:
            # Diğer timeframe'ler için hesaplama yapma, sadece saklanan D bias'ını al
            # (Aslında result['daily_bias'] bu noktada önceki 'D' döngüsünden kalanı tutar,
            # ama loglama ve netlik için get_bias ile almak daha doğru)
            result["daily_bias"] = daily_bias_analyzer.get_bias(symbol, 'D')


        # tf_label'i tanımla
        tf_label = scoring_system._get_timeframe_label(timeframe)

        # Loglama için HER ZAMAN Daily Bias'ı kullan
        daily_bias_for_log = daily_bias_analyzer.get_bias(symbol, 'D')
        bias_text = "Bullish Bias" if daily_bias_for_log == 1 else "Bearish Bias" if daily_bias_for_log == -1 else "Nötr Bias"

        # SuperTrend bilgisini log için hazırla
        st_info = ""
        if result.get("supertrend"):
            st_trend = result["supertrend"].get('trend', 'bilinmiyor').upper()
            st_info = f" | ST: {st_trend}"

        # SEMBOL-ÖZET Logunu Güncelle (Her zaman Daily Bias'ı gösterecek şekilde)
        logger.info(f"SEMBOL-ÖZET | {symbol:<8} {tf_label:<3} | {last_price_str} | {result.get('price_change_24h', 'N/A'):.2f}% | {bias_text}{st_info} | {len(result.get('patterns', []))} patterns, {len(result.get('divergences', []))} divergences")

        # FVG Loglaması (D ve 4H timeframe için ve veri varsa)
        if (timeframe == 'D' or timeframe == '240') and fvgs_list:
            try:
                # En son oluşan FVG'yi loglayalım
                last_fvg = fvgs_list[-1]
                fvg_type = last_fvg['type']
                fvg_top = last_fvg['top']
                fvg_bottom = last_fvg['bottom']
                fvg_eq = last_fvg['eq']
                fvg_filled = "Filled" if last_fvg['filled'] else "Open"

                logger.info(f"{log_prefix} Son FVG: {fvg_type} ({fvg_filled}) | Top: {fvg_top:.4f} | Bottom: {fvg_bottom:.4f} | EQ: {fvg_eq:.4f}")
            except Exception as e:
                logger.error(f"{log_prefix} FVG loglama hatası: {e}")

        # Bu kodu fonksiyondan tamamen çıkarabilirsiniz
        # veya koşullu hale getirebilirsiniz:
        try:
            if symbol == "BTCUSDT" and timeframe == scoring_system.main_timeframe and 'all_timeframe_data' in globals():
                logger.info(f"[{symbol}] All timeframe data yapısı: {list(all_timeframe_data[symbol].keys())}")
                logger.info(f"[{symbol}] Fibonacci verileri: {bool(all_timeframe_data[symbol].get('D', {}).get('fib_levels'))}")
                logger.info(f"[{symbol}] Daily Bias: {all_timeframe_data[symbol].get('D', {}).get('daily_bias')}")
        except NameError:
            logger.debug(f"[{symbol}] all_timeframe_data henüz tanımlanmamış")

        return result

    except Exception as e:
        logger.error(f"Analiz hatası ({symbol} {timeframe}): {e}")
        import traceback
        logger.error(f"Hata ayrıntıları: {traceback.format_exc()}")

    # Hata durumunda bile result döndürülmeye çalışılır
    # candles'ı tekrar eklemeye gerek yok, zaten result içinde
    return result


# Gerekli değişkenleri tanımla (Global scope)
fvrp_analyzer = FVRPAnalyzer()
fib_analyzer = FibonacciAnalyzer()  # Fibonacci için global instance ekle
order_block_analyzer = OrderBlockAnalyzer() # Order Block için global instance ekle
bos_analyzer = BOSAnalyzer()  # BOS Analyzer instance'ı eklendi
fvg_analyzer = FVGAnalyzer()  # FVG Analyzer instance'ı eklendi
smart_entry_strategy = SmartEntryStrategy()  # Smart Entry Strategy instance'ı eklendi

def calculate_stats(candles: pd.DataFrame, timeframe: str, symbol: str) -> Dict[str, Any]:
    """
    Verilen mum verileri üzerinde çeşitli istatistikleri (fiyat, değişim, EMA, VWAP, FVRP, Funding)
    hesaplar ve web uygulaması için bir sözlük olarak döndürür.

    Args:
        candles (pd.DataFrame): OHLCV mum verilerini içeren DataFrame.
        timeframe (str): Zaman dilimi string'i (örn. "60", "240").
        symbol (str): Analiz edilen sembol (örn. "BTCUSDT").

    Returns:
        Dict[str, Any]: Hesaplanan istatistikleri içeren bir sözlük.
                        Hata durumunda bazı değerler None olabilir.
    """
    stats: Dict[str, Any] = {
        'last_price': None, 'price_change': None, 'volume': None,
        'ema26': None, 'ema50': None, 'ema100': None,
        'ema26_pct': None, 'ema50_pct': None, 'ema100_pct': None,
        'vwap': None, 'vwap_diff': None,
        'poc': None, 'poc_diff': None,
        'vah': None, 'vah_diff': None,
        'val': None, 'val_diff': None,
        'funding_rate': None, 'turnover': "N/A"
    }

    if candles.empty:
        logger.warning(f"calculate_stats için boş DataFrame alındı ({symbol} {timeframe}).")
        return stats

    try:
        # Son fiyat
        stats['last_price'] = last_price = candles['close'].iloc[-1]

        # 24 saatlik değişim (eğer yeterli veri varsa)
        interval_minutes = 0
        if timeframe.isdigit():
            interval_minutes = int(timeframe)
        elif timeframe.upper() == 'D':
            interval_minutes = 1440  # 24 * 60

        if interval_minutes > 0:
            candles_per_day = 1440 // interval_minutes
            day_ago_index = - (candles_per_day + 1)  # Dünkü aynı mumun indeksi
            if len(candles) > candles_per_day:
                day_ago_price = candles['close'].iloc[day_ago_index]
                if day_ago_price != 0:
                    stats['price_change'] = ((last_price - day_ago_price) / day_ago_price) * 100
            else:
                first_price = candles['close'].iloc[0]
                if first_price != 0:
                    stats['price_change'] = ((last_price - first_price) / first_price) * 100

        # Son hacim
        stats['volume'] = candles['volume'].iloc[-1]
        stats['high'] = candles['high'].iloc[-1] # Son mumun en yüksek değeri
        stats['low'] = candles['low'].iloc[-1]   # Son mumun en düşük değeri

        # EMA hesapla
        stats['ema26'] = ema26 = candles['close'].ewm(span=26, adjust=False).mean().iloc[-1]
        stats['ema50'] = ema50 = candles['close'].ewm(span=50, adjust=False).mean().iloc[-1]
        stats['ema100'] = ema100 = candles['close'].ewm(span=100, adjust=False).mean().iloc[-1]

        # EMA fark yüzdeleri
        if ema26 != 0:
            stats['ema26_pct'] = ((last_price - ema26) / ema26) * 100
        if ema50 != 0:
            stats['ema50_pct'] = ((last_price - ema50) / ema50) * 100
        if ema100 != 0:
            stats['ema100_pct'] = ((last_price - ema100) / ema100) * 100

        # VWAP hesapla (YENİ MODÜLDEN)
        vwap_series = calculate_vwap(candles, anchor="D")
        if vwap_series is not None and not vwap_series.empty:
            stats['vwap'] = vwap = vwap_series.iloc[-1]
            if vwap is not None and vwap != 0:
                stats['vwap_diff'] = ((last_price - vwap) / vwap) * 100

        # Volume Profile hesapla (FVRPAnalyzer kullan)
        volume_profile = fvrp_analyzer.calculate_volume_profile(candles, resolution=30, va_width_pct=70)
        stats['poc'] = poc = volume_profile.get('poc')
        stats['vah'] = vah = volume_profile.get('vah')
        stats['val'] = val = volume_profile.get('val')

        if last_price is not None:
            if poc is not None and poc != 0:
                stats['poc_diff'] = ((last_price - poc) / poc) * 100
            if vah is not None and vah != 0:
                stats['vah_diff'] = ((last_price - vah) / vah) * 100
            if val is not None and val != 0:
                stats['val_diff'] = ((last_price - val) / val) * 100

        # Funding Rate
        try:
            client = BybitClient()  # Yeni instance oluştur
            stats['funding_rate'] = client.fetch_funding_rate(symbol)
        except Exception as e:
            logger.warning(f"{symbol} için Funding rate çekilirken hata: {e}")
            stats['funding_rate'] = None

        # Toplam ciro (turnover)
        if 'turnover' in candles.columns and not candles['turnover'].isnull().all():
            total_turnover = candles['turnover'].sum()
            stats['turnover'] = format_volume(total_turnover) + "$"
        else:
            if 'open' in candles.columns and 'close' in candles.columns and 'volume' in candles.columns:
                avg_price = (candles['open'] + candles['close']) / 2
                approx_turnover = (candles['volume'] * avg_price).sum()
                stats['turnover'] = format_volume(approx_turnover) + "$"
            else:
                stats['turnover'] = "N/A"

    except Exception as e:
        logger.error(f"calculate_stats içinde hata ({symbol} {timeframe}): {e}")
        import traceback
        logger.error(f"Hata ayrıntıları: {traceback.format_exc()}")

    return stats


# Sınıf/modül tanımlamaları arasına ekle
timeframe_levels_analyzer = TimeframeLevelsAnalyzer()
npoc_analyzer = NPOCAnalyzer() # NPOC için instance
sfp_analyzer = SFPAnalyzer(plen=10, lookback=24) if SFPAnalyzer else None # SFP için instance (plen=10, lookback=24)
fib_analyzer = FibonacciAnalyzer() # Fibonacci için instance
supertrend_analyzer = SuperTrendAnalyzer(atr_period=10, atr_multiplier=3.0, use_rma=True)  # SuperTrend için instance
sr_flip_analyzer = SRFlipAnalyzer(tolerance_pct=0.005)  # S/R Flip için instance (%0.5 tolerans)

# Stats Tracker
stats_tracker = StatsTracker(stats_dir="stats", main_timeframe="240")

# Global değişken bölümüne ekleyin
scoring_system = ScoringSystem(stats_tracker=stats_tracker)

# Global değişkenler kısmında başlatın
daily_bias_analyzer = DailyBiasAnalyzer()

# Günlük analiz sonuçlarını saklamak için sözlük
daily_analysis_results: Dict[str, Dict[str, Any]] = {}

def main():
    """
    Ana çalıştırma fonksiyonu.
    """
    global last_successful_sfps
    global notification_service # Bildirim servisini global yapalım
    try:
        # Konfigürasyonu yükle
        config = load_config()

        # Bildirim servisini başlat
        notification_service = NotificationService(config)

        # Log seviyesini ayarla
        try:
            logger.remove()
            # Konsol için temiz format (modül/satır bilgisi olmadan)
            logger.add(
                sys.stderr,
                level=config.get("log_level", "INFO").upper(),
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
            )
            # Dosya için de aynı temiz format
            logger.add(
                "logs/app_{time:YYYY-MM-DD_HH-mm-ss_SSSSSS}.log",
                rotation="1 day",
                retention="7 days",
                level=config.get("log_level", "INFO").upper(),
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
                encoding='utf-8'
            )
            logger.info(f"Log seviyesi {config.get('log_level', 'INFO').upper()} olarak ayarlandı.")
        except ValueError:
            logger.error(f"Geçersiz log seviyesi: {config.get('log_level')}. INFO kullanılıyor.")
            # Varsayılan INFO seviyesiyle devam et
            logger.remove()
            # Konsol için temiz format
            logger.add(
                sys.stderr,
                level="INFO",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
            )
            # Dosya için de aynı temiz format
            logger.add(
                "logs/app_{time:YYYY-MM-DD_HH-mm-ss_SSSSSS}.log",
                rotation="1 day",
                retention="7 days",
                level="INFO",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
                encoding='utf-8'
            )

        # Konfigürasyondan değerleri al
        symbols: List[str] = config.get("symbols", ["BTCUSDT"])
        timeframes: List[str] = config.get("timeframes", ["240"])
        max_candles: int = config.get("max_candles", 230)
        analysis_interval_seconds: int = config.get("analysis_interval", 180)
        npoc_sfp_update_interval_seconds: int = config.get("npoc_sfp_update_interval", 3600)

        logger.info(f"Başlatılıyor...")
        logger.info(f"Semboller: {symbols}")
        logger.info(f"Zaman Dilimleri: {timeframes}")
        logger.info(f"Maks Mum: {max_candles}")
        logger.info(f"Analiz Aralığı: {analysis_interval_seconds} saniye")
        logger.info(f"NPOC/SFP Güncelleme Aralığı: {npoc_sfp_update_interval_seconds} saniye")
        logger.info(f"Mevcut UTC: {datetime.utcnow().strftime('%d/%m/%Y %H:%M:%S')}")

        # Başlangıç mesajını gönder
        notification_service.send_startup_message()

        # İstatistik raporlayıcıyı başlat
        if notification_service.telegram_enabled and notification_service.telegram:
            logger.info("İstatistik raporlayıcı başlatılıyor...")
            stats_reporter = StatsReporter(stats_tracker, notification_service.telegram)
            stats_reporter.schedule_reporter()
            logger.info("İstatistik raporlayıcı başlatıldı - 4 saatte bir rapor gönderilecek.")

        # Başlangıç Analizleri (Sadece bir kez çalışır)
        logger.info("Tarihsel FVRP analizi başlatılıyor...")
        analyze_historical_fvrp()

        logger.info("Başlangıç Naked POC analizi...")
        npoc_analyzer.analyze_all_symbols()
        main.last_npoc_analysis = time.time() # Son analiz zamanını kaydet

        logger.info("Başlangıç SFP analizi...")
        try:
            # Başlangıçta da en son SFP'leri sakla
            initial_sfps = sfp_analyzer.analyze_all_symbols()
            last_successful_sfps = initial_sfps # Global değişkeni güncelle
            main.last_sfp_analysis = time.time()
        except Exception as e:
            logger.error(f"Başlangıç SFP analizi hatası: {e}", exc_info=True)

        logger.info("Başlangıç Zaman Dilimi Seviye analizi...")
        timeframe_levels_analyzer.analyze_all_symbols()
        # Bu analiz periyodik güncellenmiyor, sadece başlangıçta çalışıyor.
        # Veriyi bir değişkende saklayalım
        timeframe_levels_data = timeframe_levels_analyzer.get_all_levels() # get_all_levels metodu eklenmeli

        # Ana döngü
        while True:
            loop_start_time = time.time()

            # Her döngü başında puanları sıfırla
            scoring_system.reset_scores()

            # Periyodik NPOC Güncellemesi
            current_time = time.time()
            if current_time - getattr(main, 'last_npoc_analysis', 0) > npoc_sfp_update_interval_seconds:
                logger.info(f"[{npoc_sfp_update_interval_seconds // 60} dk periyot] Naked POC analizi yenileniyor...")
                try:
                    npoc_analyzer.analyze_all_symbols()
                    main.last_npoc_analysis = current_time
                except Exception as e:
                    logger.error(f"Periyodik NPOC analizi hatası: {e}", exc_info=True)


            # Periyodik SFP Güncellemesi
            if current_time - getattr(main, 'last_sfp_analysis', 0) > npoc_sfp_update_interval_seconds:
                 logger.info(f"[{npoc_sfp_update_interval_seconds // 60} dk periyot] SFP analizi yenileniyor...")
                 try:
                      # Değişiklik: analyze_all_symbols'dan dönen listeyi global değişkene ata
                      sfps_found_now = sfp_analyzer.analyze_all_symbols()
                      last_successful_sfps = sfps_found_now # Global değişkeni güncelle
                      main.last_sfp_analysis = current_time
                 except Exception as e:
                      logger.error(f"Periyodik SFP analizi hatası: {e}", exc_info=True)


            # Her döngüde analiz edilecek veriler için saklama yapıları
            all_patterns: Dict[str, List[Dict[str, str]]] = {}
            all_divergences: Dict[str, List[Dict[str, Any]]] = {}
            all_bos_results: Dict[str, Dict[str, Any]] = {}  # BOS sonuçları için yeni sözlük

            # --- Tüm zaman dilimlerinin verilerini toplayacak yapı ---
            all_timeframe_data: Dict[str, Dict[str, Dict[str, Any]]] = {}
            # Örnek yapı:
            # all_timeframe_data = {
            #     "BTCUSDT": {  # Sembol
            #         "D": {    # Zaman dilimi
            #             "patterns": [...],
            #             "divergences": [...],
            #             ...
            #         },
            #         "240": { ... }
            #     }
            # }
            # --- Veri yapısı sonu ---

            # Her sembol ve zaman dilimi için analiz yap ve PUANLA
            logger.info("--- Yeni Analiz & Puanlama Döngüsü Başladı ---")
            for symbol in symbols:
                daily_stats_for_symbol: Optional[Dict[str, Any]] = None # Günlük stats verisini saklamak için

                # Sembol için veri yapısını hazırla
                if symbol not in all_timeframe_data:
                    all_timeframe_data[symbol] = {}

                # Önce D zaman dilimini işle
                if 'D' in timeframes:
                    try:
                        results = analyze_symbol(
                            symbol, 'D', max_candles,
                            all_patterns, all_divergences, all_bos_results,
                            all_timeframe_data
                        )
                        candles_data = results.get("candles")
                        swing_points = results.get("swing_points", [])

                        # Günlük zaman dilimi verilerini all_timeframe_data yapısına ekle
                        if 'D' not in all_timeframe_data[symbol]:
                            all_timeframe_data[symbol]['D'] = {}

                        # Swing points verilerini ekle
                        all_timeframe_data[symbol]['D']['swing_points'] = swing_points

                        # Diğer verileri ekle
                        key = f"{symbol}_D"
                        patterns = all_patterns.get(key, [])
                        divergences = all_divergences.get(key, [])
                        bos_results = all_bos_results.get(key, {})

                        # Günlük stats verilerini hesapla ve ekle
                        if candles_data is not None and not candles_data.empty:
                            daily_stats_for_symbol = calculate_stats(candles_data, 'D', symbol)
                            all_timeframe_data[symbol]['D']['stats'] = daily_stats_for_symbol

                        # Diğer verileri ekle
                        all_timeframe_data[symbol]['D']['patterns'] = patterns
                        all_timeframe_data[symbol]['D']['divergences'] = divergences
                        all_timeframe_data[symbol]['D']['bos_results'] = bos_results
                        all_timeframe_data[symbol]['D']['fvgs'] = results.get("fvgs")
                        all_timeframe_data[symbol]['D']['supertrend'] = results.get("supertrend")
                        all_timeframe_data[symbol]['D']['fib_levels'] = results.get("fib_levels")

                        # Fibonacci verilerini loglayalım (debug seviyesinde)
                        if results.get("fib_levels"):
                            logger.debug(f"[{symbol}] Günlük Fibonacci verileri hesaplandı ve all_timeframe_data yapısına eklendi.")

                        # S/R Flip Analizi (Günlük pivot verilerini kullanarak)
                        sr_flip_results = {'bullish_flips': [], 'bearish_flips': []}
                        if swing_points and daily_stats_for_symbol:
                            try:
                                # Pivot verilerini hazırla
                                high_pivots = [sp for sp in swing_points if sp.get('pivot_type') == 'high']
                                low_pivots = [sp for sp in swing_points if sp.get('pivot_type') == 'low']

                                pivots_dict = {
                                    'high_pivots': high_pivots,
                                    'low_pivots': low_pivots
                                }

                                # Son fiyatı al
                                last_price = daily_stats_for_symbol.get('last_price')
                                if last_price:
                                    sr_flip_results = sr_flip_analyzer.analyze(last_price, pivots_dict)
                                    logger.debug(f"[{symbol}] S/R Flip analizi tamamlandı: {len(sr_flip_results['bullish_flips'])} bullish, {len(sr_flip_results['bearish_flips'])} bearish flip")
                                else:
                                    logger.warning(f"[{symbol}] S/R Flip analizi için son fiyat bulunamadı")
                            except Exception as sr_error:
                                logger.error(f"[{symbol}] S/R Flip analizi hatası: {sr_error}", exc_info=True)

                        # S/R Flip sonuçlarını all_timeframe_data yapısına ekle
                        all_timeframe_data[symbol]['D']['sr_flip_data'] = sr_flip_results

                        logger.info(f"[{symbol}] Günlük (D) zaman dilimi verileri all_timeframe_data yapısına eklendi. Swing points: {len(swing_points)}")
                    except Exception as e:
                        logger.error(f"{symbol} D zaman dilimi analizi hatası: {e}", exc_info=True)

                for timeframe in timeframes:
                    if timeframe == 'D':
                        continue
                    try: # Döngü içindeki olası hataları yakalamak için try bloğu
                        # 1. Analyze Symbol (candles dahil)
                        logger.debug("Analiz ediliyor: {}/{}", symbol, timeframe)
                        results = analyze_symbol(
                            symbol, timeframe, max_candles,
                            all_patterns, all_divergences, all_bos_results,
                            all_timeframe_data  # Yeni eklenen parametre
                        )
                        swing_points = results.get("swing_points", [])
                        candles_data = results.get("candles")

                        # --- Eksik Verileri Hesapla/Al ---
                        key = f"{symbol}_{timeframe}"
                        patterns = all_patterns.get(key, [])
                        divergences = all_divergences.get(key, [])
                        bos_results = all_bos_results.get(key, {})
                        logger.debug("{}/{} - Patterns: {}, Divergences: {}, BOS: {}",
                                     symbol, timeframe, len(patterns), len(divergences), bool(bos_results))

                        # Fibonacci
                        fib_data = None
                        if swing_points:
                            try:
                                fib_data = fib_analyzer.calculate_fibonacci_levels(swing_points)
                                logger.debug("{}/{} - Fibonacci hesaplandı: {}", symbol, timeframe, bool(fib_data))
                            except Exception as e:
                                logger.error("{}/{} Fibonacci hesaplama hatası: {}", symbol, timeframe, e)
                        else:
                             logger.debug("{}/{} - Fibonacci için swing noktası yok.", symbol, timeframe)


                        # Timeframe Levels
                        # DÜZELTME: Veriyi doğru anahtarla al
                        tf_levels_key = f"{symbol}_{timeframe}"
                        symbol_tf_levels_data = timeframe_levels_data.get(tf_levels_key)
                        logger.debug("{}/{} - Timeframe Levels verisi alındı (Anahtar: {}): {}", symbol, timeframe, tf_levels_key, bool(symbol_tf_levels_data))

                        # Naked Volume Areas (NVOL)
                        naked_pocs_data = []
                        logger.debug("{}/{} - Naked Veriler - POCs: {}",
                                     symbol, timeframe, len(naked_pocs_data))


                        # SFP
                        symbol_timeframe_sfps = [sfp for sfp in last_successful_sfps if
                                                 sfp.get('symbol') == symbol and
                                                 sfp.get('timeframe') == timeframe]
                        logger.debug("{}/{} - SFPs: {}", symbol, timeframe, len(symbol_timeframe_sfps))

                        # Order Blocks (12h ve 4h için)
                        order_blocks_data = None
                        if (timeframe == '720' or timeframe == '240') and candles_data is not None and not candles_data.empty:
                            try:
                                order_blocks_data = order_block_analyzer.find_latest_order_blocks(candles_data)
                                logger.debug("{}/{} - Order Blocks hesaplandı: {}", symbol, timeframe, bool(order_blocks_data))
                            except Exception as e:
                                logger.error("{}/{} Order Block analizi hatası: {}", symbol, timeframe, e)
                        elif timeframe == '720' or timeframe == '240':
                             logger.debug("{}/{} - Order Blocks için mum verisi yok.", symbol, timeframe)


                        # Stats
                        stats_data = {}
                        if candles_data is not None and not candles_data.empty:
                            try:
                                stats_data = calculate_stats(candles_data, timeframe, symbol)
                                logger.debug("{}/{} - Stats hesaplandı: {}", symbol, timeframe, bool(stats_data))
                            except Exception as e:
                                logger.error("{}/{} Stats hesaplama hatası: {}", symbol, timeframe, e)
                        else:
                             logger.debug("{}/{} - Stats için mum verisi yok.", symbol, timeframe)

                        # Daily Bias
                        daily_bias_value = None
                        try:
                            if candles_data is not None and not candles_data.empty and 'daily_bias_analyzer' in globals():
                                daily_bias_value = daily_bias_analyzer.get_bias(symbol, 'D')
                                logger.debug("{}/{} - Daily Bias: {}", symbol, timeframe, daily_bias_value)
                        except Exception as e:
                            logger.error("{}/{} Daily Bias alınırken hata: {}", symbol, timeframe, e)

                        # --- Timeframe verilerini toplama (all_timeframe_data) ---
                        # Güncel zaman diliminin verilerini birleştir
                        all_timeframe_data[symbol][timeframe] = {
                            "patterns": patterns,
                            "divergences": divergences,
                            "bos_results": bos_results,
                            "sfps": symbol_timeframe_sfps,
                            "order_blocks": order_blocks_data,
                            "stats": stats_data,
                            "naked_pocs": naked_pocs_data,
                            "fib_levels": fib_data,
                            "timeframe_levels": symbol_tf_levels_data,
                            "daily_bias": daily_bias_value,
                            "supertrend": results.get("supertrend"),
                            "swing_points": swing_points,  # MSB algoritması için swing points eklendi
                            "fvg_data": results.get("fvgs"),  # FVG verileri eklendi (impulsive hareketler için)
                            "fvgs": results.get("fvgs")  # FVG verileri eklendi (scoring_system uyumluluğu için)
                        }

                        # --- Puanlama ---
                        # Ana zaman dilimi mi? (örn. 4h)
                        if timeframe == scoring_system.main_timeframe:
                            logger.info(">>> Puanlama çağrılıyor: {}/{}", symbol, timeframe)

                            # Eğer all_timeframe_data dizisinde gereken keys yoksa ekle
                            if symbol not in all_timeframe_data:
                                all_timeframe_data[symbol] = {}
                            if timeframe not in all_timeframe_data[symbol]:
                                all_timeframe_data[symbol][timeframe] = {}

                            # Tüm gerekli verileri all_timeframe_data dizisine yerleştir
                            all_timeframe_data[symbol][timeframe]["timeframe_levels"] = symbol_tf_levels_data
                            all_timeframe_data[symbol][timeframe]["supertrend"] = results.get("supertrend")
                            all_timeframe_data[symbol][timeframe]["sfps"] = symbol_timeframe_sfps
                            all_timeframe_data[symbol][timeframe]["naked_pocs"] = naked_pocs_data

                            # 4H Fibonacci verilerini al (smart entry stratejisi için)
                            fib_data_4h = None
                            if symbol in all_timeframe_data and '240' in all_timeframe_data[symbol]:
                                fib_data_4h = all_timeframe_data[symbol]['240'].get('fib_levels')
                                if fib_data_4h:
                                    logger.info(f"[{symbol}] Giriş stratejisi için 4H Fibonacci verileri kullanılıyor.")
                                else:
                                    logger.warning(f"[{symbol}] 4H Fibonacci verileri bulunamadı.")

                            # Günlük Fibonacci verilerini al (sadece puanlama için)
                            daily_fib_data = None
                            if symbol in all_timeframe_data and 'D' in all_timeframe_data[symbol]:
                                daily_fib_data = all_timeframe_data[symbol]['D'].get('fib_levels')

                            # calculate_score metodunu çağır - Günlük Fibonacci ile puanlama yap
                            # S/R Flip verileri artık all_timeframe_data içinde günlük (D) zaman diliminde mevcut
                            score_result = scoring_system.calculate_score(
                                symbol=symbol,
                                timeframe=timeframe,
                                patterns=patterns,
                                bos_results=bos_results,
                                divergences=divergences,
                                order_blocks=order_blocks_data,
                                stats=stats_data,
                                fib_data=daily_fib_data,  # PUANLAMA İÇİN GÜNLÜK FIB KULLAN
                                daily_bias=daily_bias_value,
                                fvg_data=results.get("fvgs"),
                                all_timeframe_data=all_timeframe_data,  # S/R FLIP VERİLERİ BURADA MEVCUT
                                daily_stats=daily_stats_for_symbol,
                                fib_data_4h=fib_data_4h  # GİRİŞ STRATEJİSİ İÇİN 4H FIB KULLAN
                            )

                            # Giriş stratejisini hesapla - 4H Fibonacci kullan
                            if score_result and score_result.get('trade_direction'):
                                # 4H mum verilerini al (ATR hesaplaması için)
                                candles_4h = None
                                try:
                                    if symbol in all_timeframe_data and '240' in all_timeframe_data[symbol]:
                                        # Eğer 4H timeframe'in candles verisi all_timeframe_data içinde saklanıyorsa kullan
                                        if 'candles' in all_timeframe_data[symbol]['240']:
                                            candles_4h = all_timeframe_data[symbol]['240']['candles']
                                        # Yoksa yeniden çek
                                        else:
                                            logger.info(f"[{symbol}] ATR hesaplaması için 4H mum verileri çekiliyor...")
                                            # Yeni bir BybitClient örneği oluştur
                                            bybit_client = BybitClient()
                                            candles_4h = bybit_client.fetch_klines(symbol, '240', 50)  # ATR için 50 mum yeterli
                                    else:
                                        logger.info(f"[{symbol}] ATR hesaplaması için 4H mum verileri çekiliyor...")
                                        # Yeni bir BybitClient örneği oluştur
                                        bybit_client = BybitClient()
                                        candles_4h = bybit_client.fetch_klines(symbol, '240', 50)  # ATR için 50 mum yeterli
                                except Exception as candle_error:
                                    logger.warning(f"[{symbol}] 4H mum verileri çekilirken hata: {candle_error}")

                                # FVG verilerini al (4H timeframe için)
                                fvg_data_4h = None
                                try:
                                    if symbol in all_timeframe_data and '240' in all_timeframe_data[symbol]:
                                        fvg_data_4h = all_timeframe_data[symbol]['240'].get('fvg_data')
                                        if fvg_data_4h:
                                            logger.info(f"[{symbol}] Smart entry için 4H FVG verileri bulundu: {len(fvg_data_4h)} FVG")
                                except Exception as fvg_error:
                                    logger.warning(f"[{symbol}] Smart entry için 4H FVG verileri alınırken hata: {fvg_error}")

                                entry_levels = smart_entry_strategy.calculate_entry_levels(
                                    symbol=symbol,
                                    stats=stats_data,
                                    trade_direction=score_result['trade_direction'].lower(),
                                    fibonacci_data=fib_data_4h,  # 4H FIB KULLAN
                                    order_blocks=order_blocks_data,
                                    swing_points=swing_points,
                                    pattern_name=score_result.get('pattern_name'),
                                    candles=candles_4h,  # ATR hesaplaması için mum verilerini geçir
                                    fvg_data=fvg_data_4h  # FVG verileri eklendi (impulsive hareketler için)
                                )
                                # Hesaplanan giriş seviyelerini score_result'a ekle
                                score_result['entry_levels'] = entry_levels

                            logger.info("<<< Puanlama tamamlandı: {}/{}", symbol, timeframe)

                    except Exception as loop_error:
                         # Döngü içinde bir hata olursa logla ve devam etmeyi dene
                         logger.error("!!! Analiz/Puanlama döngüsünde hata ({}/{}): {}",
                                      symbol, timeframe, loop_error, exc_info=True)


            logger.info("--- Analiz & Puanlama Döngüsü Tamamlandı ---")

            # ----- BURADAN SONRAKİ RAPORLAMA KISIMLARI -----
            logger.debug("Pattern raporu oluşturuluyor...")
            if all_patterns:
                # --- PATTERN RAPORLAMA ---
                logger.info("=== TESPIT EDILEN PATTERNLER ===")
                sorted_pattern_keys = sorted(all_patterns.keys())
                for key in sorted_pattern_keys:
                    symbol, timeframe = key.split("_")
                    patterns = all_patterns[key]
                    for pattern in patterns:
                        # DÜZELTME: scoring_system nesnesini kullan
                        tf_label = scoring_system._get_timeframe_label(timeframe) # Etiketi scoring_system'den al
                        logger.info(
                            f"🔔 PATTERN | {symbol} {tf_label} | {pattern.get('type', '?').upper():<7} | "
                            f"{pattern.get('name', 'N/A')} (Pattern: {pattern.get('pattern', 'N/A')})"
                        )
                logger.info("===============================")
            else:
                 logger.info(">> Pattern raporu için veri yok.")

            logger.debug("Divergence raporu oluşturuluyor...")
            if all_divergences:
                # --- DIVERGENCE RAPORLAMA ---
                logger.info("=== SON TESPIT EDILEN DIVERGENCE'LAR ===")
                sorted_divergence_keys = sorted(all_divergences.keys())
                last_divergences: Dict[str, Dict[str, Any]] = {}

                for key in sorted_divergence_keys:
                    symbol, timeframe = key.split("_")
                    divergences = all_divergences[key]
                    sorted_divs = sorted(divergences, key=lambda x: x.get('pivot_time', datetime.min), reverse=True)

                    if sorted_divs:
                        last_div = sorted_divs[0]
                        last_divergences[key] = last_div

                        # DÜZELTME: scoring_system nesnesini kullan
                        tf_label = scoring_system._get_timeframe_label(timeframe) # Etiketi scoring_system'den al
                        div_type_str = last_div.get('type', '')
                        div_label = "BULL" if "Positive" in div_type_str else ("BEAR" if "Negative" in div_type_str else "??")
                        indicators_list = last_div.get('indicators', [])
                        indicator_count = len(indicators_list)
                        shortened_indicators = shorten_indicator_names(indicators_list)
                        pivot_time_dt = last_div.get('pivot_time')
                        pivot_time_str = pivot_time_dt.strftime('%d/%m/%Y %H:%M') if pivot_time_dt else "N/A"

                        logger.info(
                            f"🟍 DIVERGENCE | {symbol} {tf_label} | {div_label:<4} | "
                            f"'{indicator_count}' Ind: {shortened_indicators} | Pivot: {pivot_time_str}"
                        )
                logger.info("===================================")
            else:
                 logger.info(">> Divergence raporu için veri yok.")

            # Daily Bias Raporu (Toplu)
            logger.info("=== SON DAILY BIAS DURUMU ===")
            daily_bias_results = []
            for symbol in symbols:
                try:
                    bias_value = daily_bias_analyzer.get_bias(symbol, "D")
                    bias_text = "BULLISH" if bias_value == 1 else "BEARISH" if bias_value == -1 else "NEUTRAL"
                    bias_emoji = "🟢" if bias_value == 1 else "🔴" if bias_value == -1 else "⚪"
                    daily_bias_results.append({
                        "symbol": symbol,
                        "bias_value": bias_value,
                        "bias_text": bias_text,
                        "bias_emoji": bias_emoji
                    })
                except Exception as e:
                    logger.debug(f"Daily Bias raporu için {symbol} alınırken hata: {e}")

            if daily_bias_results:
                for res in daily_bias_results:
                    logger.info(f"{res['bias_emoji']} {res['symbol']:<10} | Daily Bias: {res['bias_text']}")
            else:
                logger.info(">> Daily Bias raporu için veri yok.")
            logger.info("===============================")

            # SuperTrend Raporu
            logger.info("=== SON SUPERTREND DURUMU ===")
            supertrend_found = False
            # SuperTrend değerlerini sembol ve timeframe'e göre listele
            supertrend_results = []

            for symbol in symbols:
                # Sadece 4 saatlik timeframe için SuperTrend raporu oluştur
                tf_to_report = [tf for tf in timeframes if tf == "240"]
                for timeframe in tf_to_report:
                    key = f"{symbol}_{timeframe}"
                    try:
                        # SuperTrend sonuçlarını analiz et
                        candles = BybitClient().fetch_klines(symbol, timeframe, 50)
                        if candles is not None and not candles.empty:
                            st_data = supertrend_analyzer.analyze_candles(symbol, timeframe, candles)
                            if st_data:
                                supertrend_results.append({
                                    'symbol': symbol,
                                    'timeframe': timeframe,
                                    'trend': st_data.get('trend', 'bilinmiyor'),
                                    'formatted_supertrend': st_data.get('formatted_supertrend', 'N/A'),
                                    'formatted_price': st_data.get('formatted_price', 'N/A'),
                                    'formatted_distance': st_data.get('formatted_distance', 'N/A'),
                                    'distance_percent': st_data.get('distance_percent', 0)
                                })
                                supertrend_found = True
                    except Exception as e:
                        logger.debug(f"SuperTrend raporu için {symbol}/{timeframe} alınırken hata: {e}")

            if supertrend_found:
                # Sonuçları sembole göre sırala
                supertrend_results = sorted(supertrend_results, key=lambda x: (x['symbol'], x['timeframe']))

                # Her sembol için sonuçları göster
                current_symbol = None
                for result in supertrend_results:
                    symbol = result['symbol']
                    timeframe = result['timeframe']
                    tf_label = scoring_system._get_timeframe_label(timeframe)
                    trend = result['trend'].upper()

                    # Emoji seçimi
                    emoji = "🟢" if trend == "UP" else "🔴" if trend == "DOWN" else "⚪"

                    # Sembole göre gruplamak için sembol değişirse başlık yazdırır
                    if current_symbol != symbol:
                        logger.info(f"-- {symbol} --")
                        current_symbol = symbol

                    # SuperTrend durumunu logla - Yeni format: Fiyat ve Uzaklık bilgileri eklendi
                    logger.info(
                        f"{emoji} {tf_label:<3}|Trend: {trend}|ST: {result['formatted_supertrend']}|"
                        f"Fiyat: {result['formatted_price']}|Uzaklık: {result['formatted_distance']} ({result['distance_percent']:.2f}%)"
                    )
            else:
                logger.info(">> SuperTrend raporu için veri yok.")
            logger.info("===============================")

            # NPOC Raporu (Mevcut kod doğru görünüyor)
            try:
                npoc_summary_path = "logs/npoc_summary.log"
                # ... (NPOC rapor kodu) ...
            except Exception as e:
                logger.error(f"NPOC özet raporu okunurken hata: {e}", exc_info=True)


            # SFP Raporu
            logger.info("=== SON TESPIT EDILEN SFP'LER ===")
            if not last_successful_sfps: # Listenin boş olup olmadığını kontrol et
                logger.info(">> Henüz başarılı bir SFP analizi tamamlanmadı veya hiç SFP bulunamadı.")
            else:
                sorted_sfps = sorted(last_successful_sfps, key=lambda x: x.get('sfp_bar_time', datetime.min), reverse=True)
                logger.info(f"Son analizde toplam {len(sorted_sfps)} SFP bulundu:")
                sfps_to_show = sorted_sfps[:10] # İlk 10'u al
                if not sfps_to_show:
                    logger.info(">> (Gösterilecek SFP yok - filtreye takıldı?)")
                else:
                    for sfp in sfps_to_show: # Döngünün çalıştığından emin olalım
                        symbol = sfp.get('symbol', 'N/A')
                        timeframe = sfp.get('timeframe', 'N/A')
                        sfp_type = sfp.get('sfp_type', 'N/A')
                        sfp_bar_time = sfp.get('sfp_bar_time')
                        failed_pivot_price = sfp.get('failed_pivot_price')

                        # DÜZELTME: Etiket için scoring_system kullan
                        tf_label = scoring_system._get_timeframe_label(timeframe)
                        time_str = sfp_bar_time.strftime('%d-%m %H:%M') if pd.notna(sfp_bar_time) else 'N/A'
                        price_str = scoring_system.format_price(failed_pivot_price) # scoring_system'deki format_price metodunu kullan

                        log_msg = (
                            f"📌 {symbol:<10} | {tf_label:<3} | {sfp_type:<7} | "
                            f"Fiyat: {price_str} | Zaman: {time_str}"
                        )
                        logger.info(log_msg)

                    if len(sorted_sfps) > 10:
                        logger.info(f"... ve {len(sorted_sfps) - 10} SFP daha (detaylar log dosyasında)")
            logger.info("===============================") # Bu satır logda görünüyor mu kontrol et


            # BOS Raporu
            if not all_bos_results: # Sözlüğün boş olup olmadığını kontrol et
                logger.info(">> BOS raporu için veri yok.")
            else:
                # BOS analizör ile özet rapor oluştur
                try:
                    bos_summary_report = bos_analyzer.generate_summary_report(all_bos_results)
                    logger.info(bos_summary_report) # Özet raporu direkt olarak loglara yazdır

                    # BOS raporunu /logs klasörüne dosya olarak kaydet
                    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
                    os.makedirs(logs_dir, exist_ok=True)

                    report_filename = "bos_summary.log"
                    report_path = os.path.join(logs_dir, report_filename)

                    # Dosya boyutu kontrolü (5MB'tan büyükse rotasyon yap)
                    try:
                        if os.path.exists(report_path) and os.path.getsize(report_path) > 5*1024*1024:  # 5MB
                            # Yedek dosya oluştur
                            backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                            backup_path = f"{report_path}.{backup_time}"
                            os.rename(report_path, backup_path)
                            logger.info(f"BOS özet dosyası büyük olduğu için yedeklendi: {backup_path}")
                    except Exception as e:
                        logger.error(f"BOS özet dosyası rotasyonu sırasında hata: {e}")

                    # Dosyaya append (ekleme) modunda yaz
                    with open(report_path, 'a', encoding='utf-8') as f:
                        f.write(f"\n=== BOS ÖZET RAPORU ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ===\n")
                        f.write("===============================\n")

                        # Bullish BOS sayılarını ekle
                        bullish_count = sum(1 for result in all_bos_results.values() if result.get('bullish'))
                        bearish_count = sum(1 for result in all_bos_results.values() if result.get('bearish'))

                        f.write(f"- Toplam Bullish BOS: {bullish_count}\n")
                        f.write(f"- Toplam Bearish BOS: {bearish_count}\n\n")

                        # BOS detaylarını ekle
                        for key, result in all_bos_results.items():
                            parts = key.split('_')
                            if len(parts) < 2:
                                continue

                            symbol = parts[0]
                            timeframe = parts[1]

                            # Bullish BOS
                            if result.get('bullish'):
                                bos_data = result['bullish']
                                price = bos_data.get('price', 0)
                                pct_change = bos_data.get('percent_change', 0)
                                timestamp = bos_data.get('timestamp')
                                time_str = timestamp.strftime('%d-%m %H:%M') if timestamp else 'N/A'

                                f.write(f"🟢 {symbol:<10} | {timeframe:<3} | Fiyat: {price} ({pct_change:+.1f}%) | {time_str}\n")

                            # Bearish BOS
                            if result.get('bearish'):
                                bos_data = result['bearish']
                                price = bos_data.get('price', 0)
                                pct_change = bos_data.get('percent_change', 0)
                                timestamp = bos_data.get('timestamp')
                                time_str = timestamp.strftime('%d-%m %H:%M') if timestamp else 'N/A'

                                f.write(f"🔴 {symbol:<10} | {timeframe:<3} | Fiyat: {price} ({pct_change:+.1f}%) | {time_str}\n")

                        f.write("\n===============================\n")

                    logger.info(f"BOS özet raporu dosyaya kaydedildi: {report_path}")
                except Exception as bos_report_error:
                    logger.error(f"BOS özet raporu oluşturulurken hata: {bos_report_error}", exc_info=True)

                logger.info("=== SON TESPIT EDILEN BOS'LAR ===")
                found_any_bos = False

                # Bullish BOS
                bull_bos_list = []
                # DÜZELTME: all_bos_results üzerinden doğru iterasyon
                for key, bos_data in all_bos_results.items():
                    bull = bos_data.get('bullish')
                    if bull:
                        symbol, timeframe = key.split("_") # key'den al
                        bull['symbol'] = symbol
                        bull['timeframe'] = timeframe
                        bull_bos_list.append(bull)

                if bull_bos_list:
                    found_any_bos = True
                    sorted_bull = sorted(bull_bos_list, key=lambda x: x.get('timestamp', datetime.min), reverse=True)
                    logger.info(f"-- Bullish BOS ({len(sorted_bull)}) --")
                    bull_to_show = sorted_bull[:5]
                    if not bull_to_show:
                        logger.info(">> (Gösterilecek Bullish BOS yok - filtreye takıldı?)")
                    else:
                        for bos in bull_to_show: # Döngü çalıştığından emin ol
                            symbol = bos.get('symbol', 'N/A')
                            timeframe = bos.get('timeframe', 'N/A')
                            price = bos.get('price', 0)
                            timestamp = bos.get('timestamp')
                            time_str = timestamp.strftime('%d-%m %H:%M') if pd.notna(timestamp) else 'N/A'
                            pct_change = bos.get('percent_change', 0)
                            # DÜZELTME: Etiket için scoring_system kullan
                            tf_label = scoring_system._get_timeframe_label(timeframe)
                            logger.info(f"🟢 {symbol:<10} | {tf_label:<3} | Fiyat: {scoring_system.format_price(price)} ({pct_change:+.1f}%) | {time_str}")

                # Bearish BOS
                bear_bos_list = []
                # DÜZELTME: all_bos_results üzerinden doğru iterasyon
                for key, bos_data in all_bos_results.items():
                    bear = bos_data.get('bearish')
                    if bear:
                        symbol, timeframe = key.split("_") # key'den al
                        bear['symbol'] = symbol
                        bear['timeframe'] = timeframe
                        bear_bos_list.append(bear)

                if bear_bos_list:
                    found_any_bos = True
                    sorted_bear = sorted(bear_bos_list, key=lambda x: x.get('timestamp', datetime.min), reverse=True)
                    logger.info(f"-- Bearish BOS ({len(sorted_bear)}) --")
                    bear_to_show = sorted_bear[:5]
                    if not bear_to_show:
                        logger.info(">> (Gösterilecek Bearish BOS yok - filtreye takıldı?)")
                    else:
                        for bos in bear_to_show: # Döngü çalıştığından emin ol
                            symbol = bos.get('symbol', 'N/A')
                            timeframe = bos.get('timeframe', 'N/A')
                            price = bos.get('price', 0)
                            timestamp = bos.get('timestamp')
                            time_str = timestamp.strftime('%d-%m %H:%M') if pd.notna(timestamp) else 'N/A'
                            pct_change = bos.get('percent_change', 0)
                            # DÜZELTME: Etiket için scoring_system kullan
                            tf_label = scoring_system._get_timeframe_label(timeframe)
                            logger.info(f"🔴 {symbol:<10} | {tf_label:<3} | Fiyat: {scoring_system.format_price(price)} ({pct_change:+.1f}%) | {time_str}")

                if not found_any_bos:
                    logger.info(">> Bu döngüde herhangi bir Break of Structure tespit edilmedi.")

                logger.info("===================================") # Bu satır logda görünüyor mu kontrol et


            # Skor Raporunu Telegram'a Gönder
            try:
                # Raporu sembol bazında sözlük olarak al (all_timeframe_data ile)
                # Minimum puan eşiği 2.0 olarak ayarlandı
                symbol_reports_dict = scoring_system.generate_score_report(all_timeframe_data, min_score_threshold=2.0)

                # Sözlüğü bildirim servisine gönder (servis her biri için ayrı mesaj atacak)
                notification_service.notify_score_report(symbol_reports_dict)

                # Loglama için raporları birleştirebilir veya ayrı ayrı loglayabiliriz
                # Şimdilik konsola bir özet yazalım
                report_summary = f"Puanlama raporu {len(symbol_reports_dict)} sembol için oluşturuldu ve gönderildi."
                logger.info(report_summary)

                # Sadece Telegram'a gönderilen sinyalleri StatsTracker'a kaydet
                try:
                    active_signal_count = stats_tracker.get_active_signals_count()
                    logger.info(f"Mevcut aktif sinyal sayısı: {active_signal_count}")

                    # symbol_reports_dict içerisindeki sinyaller Telegram'a gönderilmiş demektir
                    if symbol_reports_dict:
                        new_signals_count = 0
                        for symbol, report_text in symbol_reports_dict.items():
                            # Raporlanmış ve Telegram'a gönderilmiş sinyali bul
                            score_data = scoring_system.scores.get(symbol)
                            if score_data:
                                signal_id = stats_tracker.record_signal(score_data)
                                new_signals_count += 1
                                logger.info(f"Telegram'da raporlanan sinyal kaydedildi: {signal_id} (Skor: {score_data.get('net_score', 0):.1f})")

                        if new_signals_count > 0:
                            logger.info(f"{new_signals_count} Telegram sinyali StatsTracker'a kaydedildi.")

                            # Aktif sinyalleri kontrol et
                            current_prices = {symbol: score_data.get("entry_price") for symbol, score_data in scoring_system.scores.items() if symbol in symbol_reports_dict}
                            stats_tracker.check_active_signals(current_prices)
                    else:
                        logger.info("Bu döngüde Telegram'a gönderilen sinyal yok.")

                except Exception as stats_error:
                    logger.error(f"İstatistik takibi sırasında hata: {stats_error}", exc_info=True)

                # Raporları tek dosyaya birleştirerek kaydet (isteğe bağlı)
                try:
                    combined_report_path = "logs/scoring_report_combined.log"
                    report_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    with open(combined_report_path, "w", encoding="utf-8") as f:
                        f.write(f"Birleştirilmiş Puanlama Raporu Oluşturma Zamanı: {report_time_str}\n=====\n")
                        if not symbol_reports_dict:
                            f.write("Bu döngüde raporlanacak sinyal bulunamadı.\n")
                        else:
                            # Skorlara göre sıralayıp yazdırabiliriz
                            try:
                                def extract_score(report_text):
                                    try:
                                        if "Net Skor: *" in report_text:
                                            score_parts = report_text.split("Net Skor: *")
                                            if len(score_parts) > 1:
                                                score_text = score_parts[1].split("*")[0]
                                                return float(score_text)
                                        return 0.0  # Eğer skor bulunamazsa 0 dön
                                    except (IndexError, ValueError):
                                        return 0.0  # Herhangi bir hata durumunda 0 dön

                                sorted_symbols = sorted(symbol_reports_dict.keys(),
                                                      key=lambda sym: extract_score(symbol_reports_dict[sym]),
                                                      reverse=True)

                                for symbol in sorted_symbols:
                                    f.write(symbol_reports_dict[symbol])
                                    f.write("\n-----\n") # Ayraç
                            except Exception as sort_error:
                                logger.warning(f"Raporlar sıralanırken hata oluştu: {sort_error}")
                                # Hata olsa bile raporları yazmaya devam et (sırasız olarak)
                                for symbol, report in symbol_reports_dict.items():
                                    f.write(report)
                                    f.write("\n-----\n") # Ayraç
                    logger.info(f"Birleştirilmiş puanlama raporu kaydedildi: {combined_report_path}")
                except Exception as file_error:
                    logger.error(f"Birleştirilmiş puanlama raporu dosyaya yazılamadı: {file_error}")

            except Exception as report_error:
                logger.error(f"Puanlama raporu oluşturma/gönderme hatası: {report_error}", exc_info=True)
                notification_service.send_error_message(f"Raporlama hatası: {report_error}")

            # ----- DÖNGÜ SONU -----
            loop_end_time = time.time()
            loop_duration = loop_end_time - loop_start_time
            logger.info(f"Döngü tamamlandı. İşlem süresi: {loop_duration:.2f} saniye.")

            # Kalan süreyi hesapla ve bekle
            sleep_duration = max(0, analysis_interval_seconds - loop_duration)
            logger.info(f"Sonraki analiz için {sleep_duration:.1f} saniye bekleniyor...")
            time.sleep(sleep_duration)

    except KeyboardInterrupt:
        logger.warning("Kullanıcı tarafından durduruldu.")
        # Bildirim servisi başlatıldıysa durdurma mesajı gönder
        if 'notification_service' in globals() and notification_service.telegram_enabled:
            notification_service.send_message("⚠️ Automaton Analiz Botu durduruldu.")
        # Stats Reporter durdur
        if 'stats_reporter' in locals():
            stats_reporter.stop_reporter()
            logger.info("İstatistik raporlayıcı durduruldu.")
    except Exception as e:
        logger.critical(f"Beklenmeyen KRİTİK HATA: {e}", exc_info=True)
        # Kritik hata durumunda Telegram'a bildirim gönder
        error_details = traceback.format_exc()
        if 'notification_service' in globals() and notification_service.telegram_enabled:
            notification_service.send_error_message(f"KRİTİK HATA: {e}\n{error_details[:500]}...")
    finally:
        logger.info("Uygulama sonlandırıldı.")

if __name__ == "__main__":
    main()