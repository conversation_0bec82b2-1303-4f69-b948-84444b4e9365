# Automaton v1.3 Confluence Based Teknik Analiz Platformu

Bu uygulama, kripto para piyasalarını analiz etmek için bir dizi teknik analiz aracı sunan kapsamlı bir platformdur. Bybit borsasından verileri çekerek, gelişmiş teknik analizler gerçekleştirir ve sonuçları hem konsol çıktısı hem de interaktif bir web arayüzü aracılığıyla sunar.

## Özellikler

- **Çoklu Zaman Dilimi Analizi**: 4 saat, 12 saat ve günlük grafikler
- **Gelişmiş Teknik Göstergeler**:
  - Pivot Noktaları (HH, LL, HL, LH)
  - Divergence Tespiti (Gizli Boğa/Ayı Divergence'ları)
  - EMA (26, 50, 100)
  - VWAP (Hacim Ağırlıklı Ortalama Fiyat)
  - Fibonacci Retracement Seviyeleri (Negatif ve Genişletilmiş Seviyeler)
  - Break of Structure (BOS) Analizi
  - Order Block (OB) Tespiti
  - Swing Failure Pattern (SFP) Tespiti
  - Daily Bias Analizi
  - SuperTrend Analizi
  - Sabit Aralıklı Hacim Profili (FVRP) ve POC Analizi
  - Naked POC Analizi
  - Fair Value Gap (FVG) Analizi **Yeni!**
  - Premium/Discount Bölge Analizi **Yeni!**
- **Puanlama Sistemi**: Farklı analizlerin birleştirildiği otomatik alım/satım sinyal puanlaması
- **Akıllı Giriş Stratejisi**: Fibonacci seviyelerine dayalı giriş noktaları **Yeni!**
  - **FVG+Fibonacci Hibrit Giriş**: Impulsive hareketlerde FVG bölgeleri ile Fibonacci kombinasyonu **Yeni!**
  - **İyileştirilmiş Stop Loss Hesaplaması**: 4 saatlik swing noktalarına dayalı stop loss **Yeni!**
  - **Sadeleştirilmiş Loglama**: Kritik bilgilere odaklanan temiz log sistemi **Yeni!**
- **Gerçek Zamanlı Veri**: Bybit API üzerinden güncel veriye erişim
- **Kapsamlı Loglama**: Konsolda ve dosyalarda detaylı analiz sonuçları
- **Periyodik Analiz**: Zaman dilimlerine göre otomatik yenilenen analizler
- **Merkezi Raporlama**: Daily Bias, SuperTrend ve İşlem İstatistikleri raporları
- **İşlem İstatistikleri**: Sinyal performansını takip eden ve raporlayan sistem **Yeni!**

## Proje Mimarisi

### Ana Bileşenler

1. **Veri Toplama ve İşleme**:
   - `bybit_client.py`: Bybit API'den veri çeker
   - `data_handler.py`: Veri ön işleme ve hazırlama
   - `indicators.py`: Teknik göstergeleri hesaplar

2. **Analiz Modülleri**:
   - `pivot_analyzer.py`: Pivot noktalarını tespit eder
   - `patterns.py`: Çeşitli fiyat paternlerini tespit eder
   - `divergence_analyzer.py`: RSI/MACD uyumsuzluklarını tespit eder
   - `fvrp_analyzer.py`: Hacim profili analizi yapar
   - `npoc_analyzer.py`: Naked POC'leri tespit eder
   - `fibonacci_analyzer.py`: Fibonacci seviyelerini hesaplar
   - `bos_analyzer.py`: Break of Structure tespiti yapar
   - `sfp_analyzer.py`: Swing Failure Pattern tespiti yapar
   - `order_block_analyzer.py`: Alım/satım blokları tespit eder
   - `timeframe_levels_analyzer.py`: Zaman dilimleri arası seviye analizi yapar
   - `daily_bias_analyzer.py`: Günlük piyasa yönünü tespit eder
   - `supertrend_analyzer.py`: SuperTrend analizi yapar
   - `fvg_analyzer.py`: Fair Value Gap analizi yapar **Yeni!**
   - `premium_discount_analyzer.py`: Premium/Discount bölge analizi yapar **Yeni!**
   - `smart_entry_strategy.py`: Fibonacci tabanlı akıllı giriş stratejisi **Yeni!**

3. **Puanlama ve Raporlama Sistemi**:
   - `scoring_system.py`: Tüm analizleri puanlayarak sinyal üretir
   - `notification_service.py`: Telegram bildirimleri ve raporlama **Yeni!**
   - `stats_tracker.py`: İşlem istatistiklerini takip eder **Yeni!**
   - `stats_reporter.py`: Periyodik istatistik raporları oluşturur **Yeni!**

4. **Web Arayüzü**:
   - `app.py`: Flask web sunucusu
   - `/templates`: HTML şablonları
   - `/static`: CSS, JavaScript ve statik dosyalar

## Sistem Bileşenleri

1. **main.py** - Ana program ve koordinatör
2. **bybit_client.py** - Bybit API entegrasyonu ve veri çekme işlemleri
3. **pivot_analyzer.py** - Pivot noktası ve formasyon tespiti
4. **divergence_analyzer.py** - Divergence analizi
5. **patterns.py** - Formasyon şablonları ve tanımlamaları
6. **fvrp_analyzer.py** - FVRP ve hacim profili analizi
7. **npoc_analyzer.py** - Naked POC tespiti ve analizi
8. **timeframe_levels_analyzer.py** - Zaman dilimi bazlı seviye analizi
9. **fibonacci_analyzer.py** - Fibonacci seviye ve bölge analizi
10. **order_block_analyzer.py** - Order Block tespiti ve analizi
11. **bos_analyzer.py** - Break of Structure analizi
12. **sfp_analyzer.py** - Swing Failure Pattern analizi
13. **daily_bias_analyzer.py** - Günlük piyasa yönü tespit sistemi
14. **supertrend_analyzer.py** - SuperTrend göstergesi analizi
15. **scoring_system.py** - Otomatik sinyal puanlama sistemi
16. **fvg_analyzer.py** - **Yeni!** Fair Value Gap analizi
17. **premium_discount_analyzer.py** - **Yeni!** Premium/Discount bölge analizi
18. **smart_entry_strategy.py** - **Yeni!** Fibonacci tabanlı akıllı giriş stratejisi
19. **notification_service.py** - **Yeni!** Telegram bildirimleri ve raporlama
20. **stats_tracker.py** - İşlem istatistiklerini takip eden modül
21. **stats_reporter.py** - **Yeni!** Periyodik istatistik raporları

## Kurulum

```bash
# Gerekli kütüphaneleri yükleyin
pip install -r requirements.txt

# Gerekli çevre değişkenlerini ayarlayın
cp .env.example .env
# .env dosyasını düzenleyin

# Docker ile kurulum (isteğe bağlı)
docker-compose up -d

# Render.com ile Kurulum
# render.yaml dosyasını kullanarak Render.com'da dağıtım yapabilirsiniz
# Render Dashboard'da "Blueprint" seçeneğini kullanın ve GitHub reponuzu bağlayın
```

## Kullanım

### Komut Satırı ile Kullanım

```bash
python main.py
```

### Render.com ile Dağıtım

Uygulamayı Render.com'da dağıtmak için aşağıdaki adımları izleyin:

1. GitHub reponuzu Render.com'a bağlayın
2. "New Blueprint Instance" seçeneğini kullanın
3. GitHub reponuzu seçin
4. Render.com otomatik olarak `render.yaml` dosyasını algılayacak ve gerekli servisleri oluşturacaktır
5. Çevre değişkenlerini Render.com dashboard'dan ayarlayın:
   - API_KEY ve API_SECRET: Bybit API anahtarlarınız
   - TELEGRAM_BOT_TOKEN: Telegram bot token'ınız
   - TELEGRAM_CHAT_ID: Telegram chat ID'niz
   - Diğer gerekli değişkenler

#### Telegram Bot Kurulumu

1. Telegram'da [@BotFather](https://t.me/botfather) ile konuşarak yeni bir bot oluşturun
2. Bot token'ını alın ve Render.com'da TELEGRAM_BOT_TOKEN olarak ayarlayın
3. Botunuzu bir gruba ekleyin veya doğrudan mesaj gönderin
4. Chat ID'yi almak için [@userinfobot](https://t.me/userinfobot) kullanın
5. Chat ID'yi Render.com'da TELEGRAM_CHAT_ID olarak ayarlayın

## Yeni Özellikler (v1.3)

### Fair Value Gap (FVG) Analizi
Fair Value Gap, fiyat hareketinde oluşan boşlukları tespit eden ve bu boşlukların doldurulma potansiyelini analiz eden bir tekniktir.

**Özellikler:**
- **Boşluk Tespiti**: Ardışık üç mumda oluşan fiyat boşluklarını tespit eder
- **Bullish/Bearish FVG**: Yükseliş ve düşüş yönlü boşlukları ayrı ayrı analiz eder
- **EQ Bölgesi**: Her FVG için eşit bölge (EQ) hesaplar
- **Dolum Takibi**: FVG'lerin doldurulup doldurulmadığını takip eder

**Hesaplama Yöntemi:**
- Bullish FVG: Önceki 2. mumun düşüğü > şimdiki mumun yükseği
- Bearish FVG: Önceki 2. mumun yükseği < şimdiki mumun düşüğü
- EQ Bölgesi: (FVG Üst + FVG Alt) / 2

**Kullanım:**
```python
# FVG analizi örneği
fvgs = fvg_analyzer.find_fvgs(candles)
for fvg in fvgs:
    fvg_type = fvg["type"]  # "bullish" veya "bearish"
    top = fvg["top"]  # FVG'nin üst fiyatı
    bottom = fvg["bottom"]  # FVG'nin alt fiyatı
    eq = fvg["eq"]  # FVG'nin eşit bölgesi
    filled = fvg["filled"]  # FVG doldurulmuş mu?
```

### Premium/Discount Bölge Analizi
Fiyatın günlük pivot noktalarına göre konumunu analiz ederek, premium (yüksek) veya discount (düşük) bölgelerde olup olmadığını tespit eder.

**Özellikler:**
- **Bölge Tespiti**: Fiyatın EQ seviyesine göre premium veya discount bölgede olduğunu belirler
- **Fibonacci Entegrasyonu**: Günlük zaman dilimindeki son iki pivot noktasını kullanarak Fibonacci seviyeleri hesaplar
- **Yapı Puanlaması**: FVG ve OB gibi yapıların premium/discount bölgelere göre puanlanmasını sağlar
- **Çelişki Tespiti**: Fiyat yönü ile bölge arasındaki çelişkileri tespit eder ve puanlamaya yansıtır

**Hesaplama Yöntemi:**
- EQ Seviyesi: (Yüksek Pivot + Düşük Pivot) / 2
- Premium Bölge: Fiyat > EQ
- Discount Bölge: Fiyat < EQ

**Kullanım:**
```python
# Premium/Discount analizi örneği
pd_result = premium_discount_analyzer.calculate_daily_premium_discount(
    symbol, daily_stats, swing_points
)
is_premium = pd_result["is_premium"]
is_discount = pd_result["is_discount"]
eq_level = pd_result["eq_level"]
```

### Akıllı Giriş Stratejisi
Fibonacci seviyelerini kullanarak, fiyata en yakın optimal giriş noktalarını hesaplayan ve stop loss/take profit seviyelerini belirleyen strateji.

**Özellikler:**
- **Fibonacci Tabanlı Giriş**: Golden Spot 1 (0.34-0.382), Golden Spot 2 (0.618-0.66), OTE (0.705-0.786) gibi Fibonacci bölgelerini kullanır
- **FVG+Fibonacci Hibrit Giriş**: Impulsive hareketlerde FVG bölgeleri ile Fibonacci seviyelerinin kombinasyonu **Yeni!**
- **Pivot Tabanlı Stop Loss**: Sabit yüzde yerine, mümkün olduğunda pivot noktalarını stop loss olarak kullanır
- **Çoklu Giriş Seviyeleri**: Birincil, ikincil ve üçüncül giriş seviyeleri hesaplar
- **Strateji Bilgisi**: Hangi stratejinin kullanıldığını (fibonacci, fvg_fibonacci veya varsayılan) belirtir

**FVG+Fibonacci Hibrit Strateji:**
Bu yeni strateji, BNB örneğinde görüldüğü gibi rally-base-rally veya drop-base-drop gibi impulsive hareketlerde daha etkili giriş noktaları sağlar:

1. **FVG Tespiti**: Fiyata en yakın, doldurulmamış FVG bölgesi bulunur
2. **Fibonacci Kombinasyonu**: FVG bölgesine en yakın Fibonacci seviyesi tespit edilir
3. **Hibrit Giriş**: FVG EQ seviyesi (%60) + Fibonacci seviyesi (%40) ağırlıklı ortalaması
4. **Bölge İçi Giriş**: Trade direction'a göre FVG bölgesinin optimal kısmı (bullish için alt %30, bearish için üst %30)
5. **Final Giriş**: Hibrit giriş ile bölge içi girişin ortalaması

**Hesaplama Yöntemi:**
- **Normal Fibonacci**: Fiyata en yakın Fibonacci bölgesini veya seviyesini seçer
- **FVG+Fibonacci**: Impulsive hareket tespit edildiğinde FVG EQ + Fibonacci kombinasyonu
- **Mesafe Kontrolü**: %5'ten uzak FVG+Fibonacci girişleri reddedilir, normal Fibonacci'ye geçilir
- **Pivot Stop Loss**: 4 saatlik zaman dilimindeki son iki swing noktasından işlem yönüne uygun olanı (BULL için düşük pivot, BEAR için yüksek pivot) + %1 likidite payı
- **Triple Pattern Stop Loss**: TRIT ve TRIB patternleri için sabit %2.5 stop loss
- **Varsayılan Stop Loss**: Pivot noktası bulunamazsa, giriş fiyatının %2.5 altında/üstünde stop loss belirler

**Kullanım:**
```python
# Akıllı giriş stratejisi örneği (FVG desteği ile)
entry_levels = smart_entry_strategy.calculate_entry_levels(
    symbol, stats, trade_direction, fibonacci_data, order_blocks, swing_points,
    pattern_name, candles, fvg_data  # FVG verileri eklendi
)
primary_entry = entry_levels["primary_entry"]
stop_loss = entry_levels["stop_loss"]
take_profit = entry_levels["take_profit"]
strategy_used = entry_levels["strategy_used"]  # "fibonacci", "fvg_fibonacci" veya "default"
fvg_eq = entry_levels.get("fvg_eq")  # FVG EQ seviyesi (eğer FVG+Fib kullanıldıysa)
```

### İşlem İstatistikleri ve Raporlama
Üretilen sinyallerin performansını takip eden, işlem sonuçlarını kaydeden ve düzenli raporlar oluşturan sistem.

**Özellikler:**
- **Sinyal Takibi**: Üretilen her sinyali benzersiz bir ID ile kaydeder ve durumunu takip eder
- **Performans Metrikleri**: Başarı oranı, ortalama kar/zarar, toplam işlem sayısı gibi metrikleri hesaplar
- **Periyodik Raporlama**: 12 saatte bir (00:00 ve 12:00) performans raporu oluşturur ve Telegram'a gönderir
- **Son İşlemler Raporu**: En son tamamlanan 5 işlemin detaylarını içeren rapor oluşturur

**Raporlama Bileşenleri:**
- **Özet İstatistikler**: Toplam sinyal, tamamlanan işlem, başarı oranı, ortalama kar
- **En İyi/Kötü İşlemler**: En yüksek kar ve en yüksek zarar getiren işlemler
- **En İyi Sembol**: En yüksek başarı oranına sahip sembol ve performans detayları
- **Aktif İşlemler**: Şu anda aktif olan işlem sayısı

**Kullanım:**
```python
# İstatistik raporu örneği
stats_reporter = StatsReporter(stats_tracker, telegram_notifier)
summary_report = stats_reporter.generate_summary_report()
recent_trades = stats_reporter.generate_recent_trades_report()
```

### Geliştirilmiş Fibonacci Analizi
Fibonacci seviyelerini ve bölgelerini daha kapsamlı analiz eden, negatif değerleri ve genişletilmiş seviyeleri de içeren gelişmiş analiz.

**Özellikler:**
- **Genişletilmiş Seviyeler**: 1.0'ın üzerindeki değerler (1.114, 1.214, 1.618) ve negatif değerler (-0.618, -0.382, -0.214, -0.114)
- **Fibonacci Bölgeleri**: Golden Spot 1 (0.34-0.382), Golden Spot 2 (0.618-0.66), OTE (0.705-0.786) gibi bölgeler
- **Yeni Bölgeler**: Extend Zone 1 (0.114-0.214), Extend Zone 2 (0.786-0.886), Extend Zone 3 (1.114-1.214), Negatif Extend Zone (-0.114--0.214)
- **Yön Bazlı Puanlama**: Bullish/Bearish patternlere göre farklı bölgelerin puanlanması

**Hesaplama Yöntemi:**
- Fibonacci Seviyeleri: Swing High ve Swing Low arasındaki mesafeyi Fibonacci oranlarıyla çarpar
- Bölge Puanlaması: Bölgeler 1.5 puan, seviyeler 1.0 puan alır
- Yön Bazlı Koşullar: Extend Zone 1 ve Negatif Extend Zone sadece bearish patternlerde, Extend Zone 2 ve 3 sadece bullish patternlerde puan alır

**Kullanım:**
```python
# Fibonacci analizi örneği
fib_result = fib_analyzer.analyze_fibonacci_levels(high_price, low_price, current_price)
levels = fib_result["levels"]  # Fibonacci seviyeleri
zones = fib_result["zones"]    # Fibonacci bölgeleri
```

### Geliştirilmiş Puanlama Sistemi
Daha hassas ve kapsamlı bir puanlama sistemi ile daha güvenilir sinyaller üreten, minimum puan eşiği uygulayan sistem.

**Özellikler:**
- **Minimum Puan Eşiği**: Net puanı 2.0'ın altında olan semboller raporlanmaz
- **Premium/Discount Çelişki Puanlaması**: Bearish pattern discount bölgede veya bullish pattern premium bölgede ise -0.5 puan
- **Kalıcı Raporlar**: Sembolün pattern koşulu değişene kadar (yok olana veya yeni pivot oluşana kadar) rapor Telegram'da kalır
- **Fibonacci Tolerans Değeri**: %0.5 tolerans değeri ile daha hassas Fibonacci seviye tespiti

**Puanlama Bileşenleri:**
- **Base Score**: Ana zaman dilimindeki (4 saat) pattern ve gösterge sinyalleri
- **Confirmation Score**: Diğer zaman dilimlerindeki (12 saat, günlük) onaylayıcı sinyaller
- **Negative Score**: Çelişkili sinyaller ve olumsuz göstergeler

**Kullanım:**
```python
# Puanlama örneği
score_result = scoring_system.calculate_score(
    symbol=symbol,
    timeframe=timeframe,
    patterns=patterns,
    bos_results=bos_results,
    divergences=divergences,
    order_blocks=order_blocks_data,
    stats=stats_data,
    fib_data=daily_fib_data,
    daily_bias=daily_bias_value,
    fvg_data=fvg_data,
    all_timeframe_data=all_timeframe_data,
    daily_stats=daily_stats_for_symbol
)
```

### Sadeleştirilmiş Loglama Sistemi
Kritik bilgilere odaklanan, gereksiz detayları kaldıran temiz ve okunabilir log sistemi.

**Özellikler:**
- **Kısa ve Öz Mesajlar**: Gereksiz açıklamalar kaldırıldı, sadece kritik bilgiler gösteriliyor
- **Prefix Sistemi**: `[SL]`, `[TP]`, `[FVG]` gibi kısa prefix'lerle kategorize edilmiş loglar
- **Emoji Kullanımı**: `✅` başarı, `❌` hata durumları için görsel göstergeler
- **Debug/Info Ayrımı**: Detaylı bilgiler DEBUG seviyesinde, kritik bilgiler INFO seviyesinde

**Log Formatları:**

**Giriş Seviyesi Logları:**
```
[ETHUSDT] ✅ FVG+Fib giriş: 2461.32
[ETHUSDT] ✅ Fibonacci giriş: 2461.32
[ETHUSDT] ❌ Fibonacci giriş hesaplanamadı.
```

**Stop Loss Logları:**
```
[SL] ✅ MSB SL: 2450.15
[SL] ✅ Triple pattern SL: 2450.15 (%2.5)
[SL] ✅ Varsayılan SL: 2450.15 (%2.5)
```

**Take Profit Logları:**
```
[TP] TP1=2472.50, TP2=2485.75, TP3=2499.00
```

**Önceki vs Yeni Log Karşılaştırması:**

**Önceki (Detaylı):**
```
[ETHUSDT] Volatilite seviyesi: MEDIUM (ATR: %2.15)
[ETHUSDT] FVG verileri mevcut (3 FVG), impulsive hareket kontrolü yapılıyor...
[ETHUSDT] En yakın bullish FVG bulundu: EQ=2461.32 (Mesafe: %1.25)
[ETHUSDT] FVG'ye en yakın Fibonacci seviyesi: 0.618 (2460.15) (FVG'ye mesafe: %0.05)
[ETHUSDT] FVG+Fibonacci giriş stratejisi başarıyla hesaplandı: {...}
Stop Loss hesaplama başladı:
  Entry Price: 2461.32
  Trade Direction: bull
  Pattern Name: PHL3
  Swing Points Count: 5
```

**Yeni (Sadeleştirilmiş):**
```
[ETHUSDT] ✅ FVG+Fib giriş: 2461.32
[SL] ✅ MSB SL: 2450.15
```

**Avantajları:**
- **Daha Temiz Konsol**: Önemli bilgiler kaybolmuyor
- **Hızlı Takip**: Kritik değişiklikler kolayca görülebiliyor
- **Performans**: Daha az log yazma işlemi
- **Okunabilirlik**: Kısa ve anlaşılır mesajlar

## Lisans

MIT