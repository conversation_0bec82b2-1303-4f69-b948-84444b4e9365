"""
Pivot analizi yapan modül.
Pivot noktalarını tespit eder ve pattern analizini gerçekleştirir.
Pine Script'ten uyarlanmıştır.
"""

import numpy as np
import pandas as pd
from loguru import logger
from scipy.signal import find_peaks
from patterns import BULL_PATTERNS, BEAR_PATTERNS
from typing import List, Tuple, Dict, Any, Optional

class PivotAnalyzer:
    """Pivot noktaları ve pattern analizi yapan sınıf."""
    
    @staticmethod
    def get_pivot_length(timeframe: str) -> int:
        """
        Zaman dilimine göre pivot hesaplaması için kullanılacak 'length' değerini belirler.
        
        Args:
            timeframe (str): Zaman dilimi string'i (örn. "30", "60", "240", "720", "D").
            
        Returns:
            int: Pivot hesaplaması için kullanılacak `length` değeri.
        """
        # Tüm zaman dilimleri için aynı değeri döndür
        return 4
    
    def __init__(self, length=None, timeframe=None):
        """
        Pivot noktaları tespit eden ve zigzag oluşturan sınıf.
        
        Args:
            length: Pivot yüksekliği/derinliği için gereken mum sayısı. Belirtilmezse timeframe'e göre otomatik hesaplanır.
            timeframe: Zaman dilimi string'i. length belirtilmediğinde kullanılır.
        """
        # Eğer length belirtilmemişse ve timeframe belirtilmişse, timeframe'e göre hesapla
        if length is None and timeframe is not None:
            self.length = self.get_pivot_length(timeframe)
        else:
            self.length = length or 4  # Varsayılan değer 5
        
        logger.debug(f"PivotAnalyzer başlatıldı: length={self.length}, timeframe={timeframe}")
        
    def calculate_vwap(self, df, anchor="Day", offset=0):
        """
        VWAP (Volume Weighted Average Price) hesaplar.
        
        Args:
            df (pd.DataFrame): İşlenecek mum verileri DataFrame'i
            anchor (str): VWAP periyodu ("Day", "Week", "Month" veya "Session")
            offset (int): VWAP hesaplaması için offset değeri
            
        Returns:
            pd.Series: VWAP değerleri
        """
        # Veriyi kopyala
        df = df.copy()
        
        # Tarih sütununu datetime formatına çevir
        if 'timestamp' in df.columns:
            df['date'] = pd.to_datetime(df['timestamp'])
        else:
            logger.error("DataFrame'de timestamp sütunu bulunamadı!")
            return pd.Series(index=df.index)
        
        # Anchor periyoduna göre gruplama
        if anchor == "Day":
            df['anchor'] = df['date'].dt.date
        elif anchor == "Week":
            df['anchor'] = df['date'].dt.isocalendar().week
        elif anchor == "Month":
            df['anchor'] = df['date'].dt.month
        else:  # Session - varsayılan olarak gün
            df['anchor'] = df['date'].dt.date
        
        # Offset uygula
        if offset != 0:
            df['anchor'] = df['anchor'].shift(offset)
        
        # VWAP hesapla
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['price_volume'] = df['typical_price'] * df['volume']
        
        # Kümülatif değerleri hesapla
        df['cumulative_volume'] = df.groupby('anchor')['volume'].cumsum()
        df['cumulative_price_volume'] = df.groupby('anchor')['price_volume'].cumsum()
        
        # VWAP hesapla
        df['vwap'] = df['cumulative_price_volume'] / df['cumulative_volume']
        
        return df['vwap']
        
    def find_pivots(self, df):
        """
        TradingView/PineScript'in ta.highestbars ve ta.lowestbars mantığıyla uyumlu pivot noktası tespiti yapar.
        Her bar için, kendisi dahil önceki 'length' kadar barın en yükseği/düşüğü olup olmadığını kontrol eder.
        """
        df = df.copy()
        length = self.length
        highs = df['high']
        lows = df['low']
        n = len(df)
        
        # Rolling window ile her bar için geriye dönük 'length' barın max/min değerini bul
        # window=length -> mevcut bar ve önceki length-1 barı içerir
        rolling_high_max = highs.rolling(window=length, min_periods=1).max()
        rolling_low_min = lows.rolling(window=length, min_periods=1).min()
        
        # Mevcut barın high değeri, penceredeki max high değerine eşit mi?
        is_pivot_high_potential = (highs == rolling_high_max)
        # Mevcut barın low değeri, penceredeki min low değerine eşit mi?
        is_pivot_low_potential = (lows == rolling_low_min)
        
        # Pivot sütunlarını başlat
        df['pivot_high'] = np.nan
        df['pivot_low'] = np.nan
        df['direction'] = None # Yönü takip etmek için (ph veya pl)
        
        # Pivotları ata - PineScript'teki gibi high pivot öncelikli
        # Eğer bir bar hem high hem low pivot potansiyeli taşıyorsa, high olarak işaretlenir.
        df.loc[is_pivot_high_potential, 'pivot_high'] = highs[is_pivot_high_potential]
        df.loc[is_pivot_high_potential, 'direction'] = 'ph'
        
        # Sadece high pivot olmayan potansiyel low pivotları işaretle
        is_valid_low = is_pivot_low_potential & ~is_pivot_high_potential
        df.loc[is_valid_low, 'pivot_low'] = lows[is_valid_low]
        df.loc[is_valid_low, 'direction'] = 'pl' # Yönü sadece geçerli low pivotlar için güncelle
        
        # Rolling window'un tam olmadığı başlangıçtaki olası pivotları temizle (opsiyonel ama önerilir)
        # PineScript ta.highestbars/lowestbars ilk length-1 barda NA döndürmez ama bu mantık daha güvenli olabilir.
        # df.iloc[:length-1, df.columns.get_loc('pivot_high')] = np.nan
        # df.iloc[:length-1, df.columns.get_loc('pivot_low')] = np.nan
        # df.iloc[:length-1, df.columns.get_loc('direction')] = None
        
        # Loglama güncellemesi
        pivot_high_count = df['pivot_high'].notna().sum()
        pivot_low_count = df['pivot_low'].notna().sum()
        logger.debug(f"PineScript (highest/lowest==0) mantığıyla tespit edilen pivotlar: Yüksek: {pivot_high_count}, Düşük: {pivot_low_count}")
        
        return df
    
    def identify_swings(self, df):
        """
        Pivot noktalarından swing patternleri (HH, LL, HL, LH) tespit eder.
        Pine Script'teki zigzag mantığını birebir uygular.
        
        Args:
            df (pd.DataFrame): Pivot noktaları tespit edilmiş DataFrame
            
        Returns:
            pd.DataFrame: Swing pattern bilgisi eklenmiş DataFrame
            list: Pivot noktaları ve pattern bilgilerini içeren liste
        """
        # Veriyi kopyala
        df = df.copy()
        
        # Pine Script'teki zigzag mantığı için gerekli veri yapıları
        zigzag_price = []  # Zigzag fiyatları - Pine Script'te array.new_float(0)
        zigzag_barindex = []  # Zigzag mum indeksleri - Pine Script'te array.new_int(0)
        zigzag_hhll = []  # Zigzag HL, HH, LL, LH dizisi - Pine Script'te array.new_string(0)
        zigzag_times = []  # Zigzag zaman damgaları
        
        # Son yön (ph: pivot high, pl: pivot low)
        last_direction = None
        
        # Pivot noktaları içeren satırları filtrele
        pivot_df = df[df['direction'].notna()].copy()
        
        # Hiç pivot yoksa boş DataFrame ve liste döndür
        if pivot_df.empty:
            logger.warning("Pivotlar tespit edilemedi!")
            return df, []
        
        # Pivot satırlarını ve indekslerini detaylı olarak logla
        logger.debug(f"Pivot satırları: {len(pivot_df)}")
        pivot_high_count = len(pivot_df[pivot_df['direction'] == 'ph'])
        pivot_low_count = len(pivot_df[pivot_df['direction'] == 'pl'])
        logger.debug(f"Yüksek pivotlar: {pivot_high_count}, Düşük pivotlar: {pivot_low_count}")
        
        # Tüm pivot noktalarını tarih sırasında işle
        for i, row in pivot_df.iterrows():
            current_direction = row['direction']
            price = row['pivot_high'] if current_direction == 'ph' else row['pivot_low']
            bar_index = df.index.get_loc(i)
            
            logger.debug(f"İşleniyor: {row['timestamp']}, Yön: {current_direction}, Fiyat: {price}")
            
            # Eğer yön değiştiyse veya ilk pivot ise
            if current_direction != last_direction:
                # Yeni pivot noktasını ekle
                if zigzag_price:
                    # Yeni pivot eklenmeden önce son eklenen pivot ile aynı yöndeyse, ekleme
                    if (current_direction == 'ph' and last_direction == 'ph' and price > zigzag_price[0]) or \
                       (current_direction == 'pl' and last_direction == 'pl' and price < zigzag_price[0]):
                        zigzag_price[0] = price
                        zigzag_barindex[0] = bar_index
                        zigzag_times[0] = row['timestamp']
                        logger.debug(f"Aynı yönde daha ekstrem pivot: {current_direction}, Fiyat: {price}")
                    else:
                        # Yeni pivot noktasını diziye ekle (unshift - başa ekle)
                        zigzag_price.insert(0, price)
                        zigzag_barindex.insert(0, bar_index)
                        zigzag_times.insert(0, row['timestamp'])
                        logger.debug(f"Yeni pivot eklendi: {current_direction}, Fiyat: {price}")
                        
                        # En az 3 pivot olduğunda swing tipini belirle
                        if len(zigzag_price) >= 3:
                            # Pine Script mantığı birebir uygula:
                            # _text = direction == "ph" ? array.get(zigzag_price, 0) > array.get(zigzag_price, 2) ? 'HH' : 'LH' : array.get(zigzag_price, 0) < array.get(zigzag_price, 2) ? 'LL' : 'HL'
                            if current_direction == 'ph':  # Yüksek pivot
                                swing_type = 'HH' if zigzag_price[0] > zigzag_price[2] else 'LH'
                            else:  # Düşük pivot
                                swing_type = 'LL' if zigzag_price[0] < zigzag_price[2] else 'HL'
                            
                            # Swing tipi ekle (unshift - başa ekle)
                            zigzag_hhll.insert(0, swing_type)
                            # DataFrame'e ekle
                            df.loc[i, 'swing_pattern'] = swing_type
                            logger.debug(f"Swing tipi belirlendi: {swing_type}")
                else:
                    # İlk pivot noktası
                    zigzag_price.append(price)
                    zigzag_barindex.append(bar_index)
                    zigzag_times.append(row['timestamp'])
                    logger.debug(f"İlk pivot eklendi: {current_direction}, Fiyat: {price}")
                
                # Yönü güncelle
                last_direction = current_direction
            else:
                # Aynı yöndeki daha ekstrem değerle güncelle
                if (current_direction == 'ph' and price > zigzag_price[0]) or \
                   (current_direction == 'pl' and price < zigzag_price[0]):
                    # Son pivotu güncelle
                    zigzag_price[0] = price
                    zigzag_barindex[0] = bar_index
                    zigzag_times[0] = row['timestamp']
                    logger.debug(f"Mevcut pivot güncellendi: {current_direction}, Fiyat: {price}")
                    
                    # Eğer zigzag'de en az 3 nokta varsa swing tipini güncelle
                    if len(zigzag_price) >= 3:
                        if current_direction == 'ph':  # Yüksek pivot
                            swing_type = 'HH' if zigzag_price[0] > zigzag_price[2] else 'LH'
                        else:  # Düşük pivot
                            swing_type = 'LL' if zigzag_price[0] < zigzag_price[2] else 'HL'
                        
                        # İlk swing tipini güncelle
                        if zigzag_hhll:
                            zigzag_hhll[0] = swing_type
                            # DataFrame'de güncelle
                            df.loc[i, 'swing_pattern'] = swing_type
                            logger.debug(f"Swing tipi güncellendi: {swing_type}")
        
        # Zigzag veri yapıları hakkında bilgi ver
        logger.debug(f"Zigzag price uzunluğu: {len(zigzag_price)}")
        logger.debug(f"Zigzag barindex uzunluğu: {len(zigzag_barindex)}")
        logger.debug(f"Zigzag hhll uzunluğu: {len(zigzag_hhll)}")
        logger.debug(f"Zigzag times uzunluğu: {len(zigzag_times)}")
        
        # Swing noktaları listesi oluştur
        swing_points = []
        
        # Veri yapılarının boyutlarını kontrol et
        min_length = min(len(zigzag_price), len(zigzag_barindex), len(zigzag_times))
        
        # zigzag_hhll'nin boyutunu kontrol et - genellikle zigzag_price'dan 2 eksik olur
        hhll_offset = len(zigzag_price) - len(zigzag_hhll)
        
        # Swing noktalarını oluştur
        for i in range(len(zigzag_hhll)):
            idx = i
            price = zigzag_price[idx]
            bar_idx = zigzag_barindex[idx]
            timestamp = zigzag_times[idx]
            swing_type = zigzag_hhll[i]
            
            # Pivot tipini belirle
            pivot_type = 'high' if swing_type in ['HH', 'LH'] else 'low'
            
            # Swing noktası oluştur
            swing_point = {
                'index': bar_idx,
                'timestamp': timestamp,
                'price': price,
                'type': swing_type,
                'pivot_type': pivot_type
            }
            
            # Listeye ekle
            swing_points.append(swing_point)
        
        # İlk iki pivot için swing noktaları oluştur (HH/LL/HL/LH yok)
        for i in range(hhll_offset):
            idx = i + len(zigzag_hhll)  # Offset hesapla
            if idx < len(zigzag_price):
                price = zigzag_price[idx]
                bar_idx = zigzag_barindex[idx]
                timestamp = zigzag_times[idx]
                
                # Son pivotun tipine göre belirle
                if idx > 0:
                    last_price = zigzag_price[idx-1]
                    pivot_type = 'high' if price > last_price else 'low'
                else:
                    # İlk pivot için varsayılan tip
                    pivot_type = 'high' if price > df['close'].mean() else 'low'
                
                # Swing noktası oluştur (HH/LL/HL/LH olmadığında)
                swing_point = {
                    'index': bar_idx,
                    'timestamp': timestamp,
                    'price': price,
                    'type': '',  # Swing tipi yok
                    'pivot_type': pivot_type
                }
                
                # Listeye ekle
                swing_points.append(swing_point)
        
        # Swing noktalarını zamana göre sırala
        swing_points.sort(key=lambda x: x['index'])
        
        # Son HHLL dizisini logla
        if len(zigzag_hhll) > 0:
            hhll_string = ','.join(zigzag_hhll)
            logger.debug(f"Son HHLL dizisi: {hhll_string}")
        
        # Swing sayısını logla
        logger.debug(f"Tespit edilen swing noktaları sayısı: {len(swing_points)}")
        
        return df, swing_points
    
    def is_matching(self, swing_sequence, pattern_str):
        """
        Pine Script'teki isMatching fonksiyonunu birebir uygular.
        
        Args:
            swing_sequence (str): Kontrol edilecek swing dizisi (örn. "HHLLHLHH")
            pattern_str (str): Kontrol edilecek pattern dizisi (örn. "LL,LH,LL,HH,HL")
            
        Returns:
            bool: Pattern eşleşiyorsa True, yoksa False
        """
        if not pattern_str or len(pattern_str.strip()) == 0:
            return False
        
        # Pattern stringini temizle
        pattern_str = pattern_str.strip().replace(' ', '').upper()
        
        # Pattern stringini virgüllerle böl
        pattern_parts = pattern_str.split(',')
        
        # Pattern parçalarını ters çevir
        pattern_parts.reverse()
        
        # Swing dizisinin son kısmını kontrol et
        # Her bir pattern parçası için swing dizisinde eşleşme ara
        current_pos = len(swing_sequence) - 1  # Swing dizisinin sonundan başla
        
        for part in pattern_parts:
            # Eğer swing dizisinin dışına çıktıysak, eşleşme yok
            if current_pos < len(part) - 1:
                return False
            
            # Swing dizisinin ilgili kısmını al
            swing_part = swing_sequence[current_pos - len(part) + 1:current_pos + 1]
            
            # Eşleşme kontrolü
            if swing_part != part:
                return False
            
            # Bir sonraki pozisyona geç
            current_pos -= len(part)
        
        return True
    
    def detect_patterns(self, swing_points, timeframe=None):
        """
        Swing noktalarında belirli pattern'leri tespit eder.
        
        Args:
            swing_points (list): Swing noktaları listesi
            timeframe (str, optional): Zaman dilimi.
            
        Returns:
            list: Tespit edilen pattern'ler listesi
        """
        if len(swing_points) < 5:
            return []
        
        # Sadece tipi olan swing noktalarını al
        swings_with_type = [sp for sp in swing_points if sp.get('type')]
        
        # Son 15 swing noktasını al
        last_swings = swings_with_type[-15:] if len(swings_with_type) >= 15 else swings_with_type
        
        # Swing tiplerini birleştir
        swing_sequence = ''.join([s['type'] for s in last_swings])
        
        # Debug log ekle
        logger.debug(f"Swing dizisi: {swing_sequence}")
        
        # Tespit edilen pattern'leri sakla
        detected_patterns = []
        
        # Pattern eşleştirme fonksiyonunu sınıf metodu yap
        def check_pattern(pattern_name, pattern_def, pattern_type):
            # Pattern tanımı string mi yoksa dict mi kontrol et
            if isinstance(pattern_def, dict):
                pattern_str = pattern_def['pattern']
                pattern_display_name = pattern_def['name']
            else:
                pattern_str = pattern_def
                pattern_display_name = pattern_name
            
            # Eşleşme kontrolü - Pine Script mantığı
            if self.is_matching(swing_sequence, pattern_str):
                logger.debug(f"Pattern eşleşti: {pattern_display_name}")
                
                # SL için yapısal pivot fiyatını bul
                structure_pivot_price = None
                
                # Yapısal pivot noktasını belirle (pattern'e göre)
                if pattern_type == 'Bullish':
                    # Bullish pattern'lerde kritik pivot genellikle son Higher Low (HL) noktasıdır
                    # İlk çalışan pivot noktasını bulmak için son patterndeki pivot tiplerini analiz et
                    pattern_parts = pattern_str.split(',')
                    
                    # Örnek: 'LL,LH,LL,HH,HL' pattern'i için son HL pivotunu bul
                    # Sondan başa doğru tarayarak ilk 'HL' pivotunu bulalım
                    for i in range(len(pattern_parts)-1, -1, -1):
                        if pattern_parts[i] == 'HL':
                            # Son HL pivot indeksini hesapla
                            pivot_index = len(last_swings) - (len(pattern_parts) - i)
                            if 0 <= pivot_index < len(last_swings):
                                hl_pivot = last_swings[pivot_index]
                                structure_pivot_price = hl_pivot.get('price')
                                logger.debug(f"Bullish pattern için SL pivot noktası: HL (Fiyat: {structure_pivot_price})")
                            break
                
                elif pattern_type == 'Bearish':
                    # Bearish pattern'lerde kritik pivot genellikle son Lower High (LH) noktasıdır
                    pattern_parts = pattern_str.split(',')
                    
                    # Sondan başa doğru tarayarak ilk 'LH' pivotunu bulalım
                    for i in range(len(pattern_parts)-1, -1, -1):
                        if pattern_parts[i] == 'LH':
                            # Son LH pivot indeksini hesapla
                            pivot_index = len(last_swings) - (len(pattern_parts) - i)
                            if 0 <= pivot_index < len(last_swings):
                                lh_pivot = last_swings[pivot_index]
                                structure_pivot_price = lh_pivot.get('price')
                                logger.debug(f"Bearish pattern için SL pivot noktası: LH (Fiyat: {structure_pivot_price})")
                            break
                
                # Pattern verisini oluştur
                pattern_data = {
                    'name': pattern_display_name,
                    'pattern': pattern_str,
                    'type': pattern_type,
                    'structure_pivot_price': structure_pivot_price  # SL için pivot fiyatı
                }
                
                # Eğer pattern fiyatı bulunamazsa bunu loglayalım
                if structure_pivot_price is None:
                    logger.warning(f"Pattern {pattern_display_name} için yapısal pivot fiyatı bulunamadı!")
                
                detected_patterns.append(pattern_data)
        
        # Tüm zaman dilimleri için tüm patternleri kontrol et
        # Boğa patternleri
        for pattern_name, pattern_def in BULL_PATTERNS.items():
            check_pattern(pattern_name, pattern_def, 'Bullish')
        
        # Ayı patternleri
        for pattern_name, pattern_def in BEAR_PATTERNS.items():
            check_pattern(pattern_name, pattern_def, 'Bearish')
        
        return detected_patterns 