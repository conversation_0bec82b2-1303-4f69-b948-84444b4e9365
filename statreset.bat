@echo off
REM trade_signals.csv başlık satırı
echo signal_id,symbol,timeframe,entry_time,entry_price,direction,score,sl_price,tp_price,tp1_price,tp2_price,tp3_price,status,confirmations,result_time,result_price,profit_percentage,volatility_level,fib_level > stats\trade_signals.csv

REM trade_results.csv başlık satırı
echo signal_id,symbol,timeframe,entry_time,entry_price,direction,score,sl_price,tp_price,tp1_price,tp2_price,tp3_price,status,confirmations,result_time,result_price,profit_percentage,volatility_level,fib_level > stats\trade_results.csv

REM backups klasöründeki tüm dosyaları temizle
echo Backups klasörünü temizleme...
del /Q stats\backups\*.* 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Backup dosyaları başarı<PERSON> silindi.
) else (
    echo Backup klasörü zaten boş ya da erişilemiyor.
)

REM performance_metrics.json sıfırlama (yeni TP metrikleri ile)
echo {^
    "total_signals": 0,^
    "completed_trades": 0,^
    "successful_trades": 0,^
    "failed_trades": 0,^
    "success_rate": 0.0,^
    "avg_profit_percentage": 0.0,^
    "total_profit_percentage": 0.0,^
    "tp1_hits": 0,^
    "tp2_hits": 0,^
    "tp3_hits": 0,^
    "tp1_rate": 0.0,^
    "tp2_rate": 0.0,^
    "tp3_rate": 0.0,^
    "best_trade": null,^
    "worst_trade": null,^
    "best_symbol": null,^
    "best_confirmation": null,^
    "last_updated": "2025-04-20T00:00:00Z"^
} > stats\performance_metrics.json

REM LOG dosyalarını temizle
> logs\bos_summary.log echo.
> logs\fibonacci_summary.log echo.
> logs\fvrp_history.log echo.
> logs\npoc_analysis.log echo.
> logs\npoc_summary.log echo.
> logs\timeframe_levels.log echo.
> logs\sfp_analysis.log echo.

REM Sadece bugüne ait app_2025 logları kalsın, diğerlerini sil
for %%F in (logs\app_2025-*.log) do (
    set "fname=%%~nxF"
    setlocal enabledelayedexpansion
    set "today=%DATE:~0,4%-%DATE:~5,2%-%DATE:~8,2%"
    echo !fname! | findstr /C:"app_!today!_" >nul
    if errorlevel 1 del "logs\%%~nxF" && echo %%~nxF silindi.
    endlocal
)

echo İstatistik ve log dosyaları sıfırlandı (TP metrikleri dahil).
echo Yeni özellikler: TP1, TP2, TP3 başarı oranları takibi eklendi.
pause
