import pandas as pd
from typing import Dict, Any, Optional, Tuple
from loguru import logger
from utils import format_price_standard

class DailyBiasAnalyzer:
    """
    Önceki günün yüksek ve düşük seviyelerine göre günl<PERSON><PERSON> eğilim (bias) belirleyen sınıf.
    TTrades Daily Bias [TFO] göstergesinin mantığını kullanır.
    """
    def __init__(self):
        self.bias_data = {}  # Sembol ve timeframe'e göre bias verilerini saklar
        logger.info("DailyBiasAnalyzer başlatıldı")
    
    def _init_symbol_data(self, symbol: str, timeframe: str) -> None:
        """Yeni bir sembol için veri yapısını başlatır"""
        key = f"{symbol}_{timeframe}"
        if key not in self.bias_data:
            self.bias_data[key] = {
                "prev_high": None,  # Önceki günün yü<PERSON> (PDH)
                "prev_low": None,   # Önceki günün <PERSON> (PDL)
                "current_high": None,  # Mevcut günün yüks<PERSON>ği
                "current_low": None,   # Mevcut günün düşüğü
                "prev_open": None,     # Önceki günün açılışı
                "prev_up": False,      # Önceki günün trendi yukarı yönlü mü
                "bias": 0              # Bias: 1 (Bullish), -1 (Bearish), 0 (Nötr)
            }
    
    def determine_bias(self, symbol: str, timeframe: str, candles: pd.DataFrame) -> int:
        """
        Verilen mum verilerine göre günlük eğilimi (bias) belirler
        
        Args:
            symbol: İşlem sembolü
            timeframe: Zaman dilimi
            candles: OHLCV verileri içeren DataFrame
            
        Returns:
            int: 1 (Bullish Bias), -1 (Bearish Bias), 0 (No Bias)
        """
        if candles.empty or len(candles) < 2:
            logger.warning(f"{symbol} {timeframe} için yeterli veri yok, bias hesaplanamadı")
            return 0
            
        self._init_symbol_data(symbol, timeframe)
        key = f"{symbol}_{timeframe}"
        data = self.bias_data[key]
        
        # Son iki mumu al
        prev_candle = candles.iloc[-2]
        current_candle = candles.iloc[-1]
        
        # Veri hazırlığı ve hesaplama
        firstRun = False  # İlk çalıştırma mı kontrolü
        
        # Önceki veri yok ise ilk kez hesaplıyoruz demektir
        if data["prev_high"] is None:
            firstRun = True
            # İlk hesaplama için veri yapısını doldur
            data["prev_high"] = prev_candle["high"]
            data["prev_low"] = prev_candle["low"]
            data["current_high"] = current_candle["high"]
            data["current_low"] = current_candle["low"]
            data["prev_open"] = prev_candle["open"]
            data["prev_up"] = prev_candle["close"] > prev_candle["open"]
            logger.info(f"{symbol} {timeframe} için ilk bias hesaplaması yapılıyor")
        # Normal çalışma durumu - günlük değişim varsa verileri güncelle
        elif timeframe.upper() == 'D' or timeframe == '1440':
            # Önceki günün değerlerini sakla
            data["prev_high"] = data["current_high"]
            data["prev_low"] = data["current_low"]
            data["prev_open"] = current_candle["open"]
            data["prev_up"] = current_candle["close"] > current_candle["open"]
            
            # Yeni günün değerlerini güncelle
            data["current_high"] = current_candle["high"]
            data["current_low"] = current_candle["low"]
        else:
            # Günlük olmayan timeframe'lerde doğrudan mevcut mumu kullan
            data["current_high"] = current_candle["high"]
            data["current_low"] = current_candle["low"]
        
        # Yukarıya taşındı
        
        # Değerleri yerel değişkenlerde sakla daha kolay erişim için
        ph = data["prev_high"]
        pl = data["prev_low"]
        ch = data["current_high"]
        cl = data["current_low"]
        close = current_candle["close"]
        p_up = data["prev_up"]
        
        # TTrades Daily Bias Mantığını Uygula
        bias = 0
        bias_reason = ""
        
        # Kural 1: Kapanış önceki yüksekten büyükse -> Bullish Bias
        if close > ph:
            bias = 1
            bias_reason = "Kapanış PDH üzerinde"
        
        # Kural 2: Kapanış önceki düşükten küçükse -> Bearish Bias
        elif close < pl:
            bias = -1
            bias_reason = "Kapanış PDL altında"
        
        # Kural 3: Kapanış içeride, günün yükseği PDH üzerinde, düşüğü PDL üzerindeyse
        elif close < ph and close > pl and ch > ph and cl > pl:
            bias = -1
            bias_reason = "PDH üzerine çıkamadı"
        
        # Kural 4: Kapanış içeride, günün yükseği PDH altında, düşüğü PDL altındaysa
        elif close > pl and close < ph and ch < ph and cl < pl:
            bias = 1
            bias_reason = "PDL altına inemedi"
        
        # Kural 5: Günün yükseği PDH altında/eşit, düşüğü PDL üstünde/eşitse -> önceki trend devam eder
        elif ch <= ph and cl >= pl:
            bias = 1 if p_up else -1
            bias_reason = "Inside bar - Önceki trend devam ediyor"
        
        # Kural 6: Diğer durumlar -> No Bias
        else:
            bias = 0
            bias_reason = "Outside bar - eğilim belirsiz"
        
        # Sonucu sakla
        data["bias"] = bias
        
        # Log mesajı oluştur
        bias_text = "Bullish Bias" if bias == 1 else "Bearish Bias" if bias == -1 else "Nötr"
        
        # İlk çalıştırmada daha detaylı log yazalım
        if firstRun:
            logger.info(f"Daily Bias: {symbol} {timeframe}: {bias_text} - {bias_reason} [ilk hesaplama]") 
        else:
            logger.info(f"Daily Bias: {symbol} {timeframe}: {bias_text} - {bias_reason}")
            
        logger.debug(f"PDH: {format_price_standard(ph)}, PDL: {format_price_standard(pl)}, " 
                     f"CH: {format_price_standard(ch)}, CL: {format_price_standard(cl)}, "
                     f"Close: {format_price_standard(close)}")
        
        return bias
    
    def get_bias(self, symbol: str, timeframe: str) -> int:
        """Belirli bir sembol ve timeframe için mevcut eğilimi döndürür"""
        key = f"{symbol}_{timeframe}"
        if key in self.bias_data:
            return self.bias_data[key]["bias"]
        return 0 