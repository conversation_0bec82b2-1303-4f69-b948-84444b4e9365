import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
from utils import format_price_standard

class FibonacciAnalyzer:
    """
    Pivot noktalarına dayalı Fibonacci Retracement seviyelerini hesaplar.

    <PERSON><PERSON><PERSON><PERSON><PERSON>, son üç swing noktasının ilk ikisi kullanılarak yapılır.
    Örn: LL, LH, HL -> LL'den LH'ye Fib çekilir.
    Örn: HL, LL, LH -> HL'den LL'ye Fib çekilir.

    Fibonacci seviyeleri her zaman yüksek swingler 0, düşük swingler 1 olacak şekilde hesaplanır.
    <PERSON><PERSON>, Fibonacci yüksekten aşağı doğru da çekilse veya alçaktan yükseğe doğru da çekilse,
    her zaman yüksek fiyat 0.0, düş<PERSON>k fiyat 1.0 olarak ayarlanır.
    """
    def __init__(self):
        # <PERSON><PERSON>mlı Fibonacci oranları ve özel bölgeler
        # <PERSON><PERSON><PERSON>: <PERSON><PERSON>, <PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON>/Seviye Adı
        self.levels_config = {
            -1.618: "-1.618 (Neg Ext)",
            -1.214: "-1.214 (Neg Ext)",
            -1.114: "-1.114 (Neg Ext)",
            -1.0: "-1.0 (Neg Ext)",
            -0.886: "-0.886 (Neg Ext)",
            -0.786: "-0.786 (Neg Ext)",
            -0.705: "-0.705 (Neg Ext)",
            -0.66: "-0.66 (Neg Ext)",
            -0.618: "-0.618 (Neg Ext)",
            -0.5: "-0.5 (Neg Ext)",
            -0.382: "-0.382 (Neg Ext)",
            -0.34: "-0.34 (Neg Ext)",
            -0.214: "-0.214 (Neg Ext)",
            -0.114: "-0.114 (Neg Ext)",
            0.0: "0.0 (Start/End)",
            0.114: "Extend Low",
            0.214: "Extend High",
            0.295: "Bearish OTE Low",
            0.34: "Golden Spot 1 Low",
            0.382: "Golden Spot 1 High",
            0.5: "EQ",
            0.618: "Golden Spot 2 Low",
            0.66: "Golden Spot 2 High",
            0.705: "OTE",
            0.786: "Extend 2 Low",
            0.886: "Extend 2 High",
            1.0: "1.0 (Start/End)",
            1.114: "1.114 (Ext)",
            1.214: "1.214 (Ext)",
            1.618: "1.618 (Ext)"
        }
        # Hesaplama için oranları sırala
        self.sorted_ratios = sorted(self.levels_config.keys())

    def _get_fib_points(self, swing_points: List[Dict[str, Any]]) -> Optional[Tuple[Dict[str, Any], Dict[str, Any], str]]:
        """
        Fibonacci hesaplaması için kullanılacak iki swing noktasını ve son swing tipini belirler.

        Returns:
            Tuple (start_swing, end_swing, last_swing_type) veya None
        """
        # Yalnızca tipi olan geçerli swing noktalarını filtrele
        valid_swings = [sp for sp in swing_points if sp.get('type')]
        valid_swings.sort(key=lambda x: x['index']) # Zaman sırasına göre sırala

        if len(valid_swings) < 3:
            logger.debug(f"Fibonacci için yeterli swing noktası bulunamadı (en az 3 gerekli, {len(valid_swings)} bulundu).")
            return None

        # Son üç geçerli swing noktasını al
        last_three_swings = valid_swings[-3:]
        last_swing_type = last_three_swings[-1]['type'] # Son swing tipini sakla

        # İlk ikisini Fibonacci için kullan
        start_swing = last_three_swings[0]
        end_swing = last_three_swings[1]

        logger.debug(f"Fibonacci noktaları seçildi: {start_swing.get('type')} ({format_price_standard(start_swing.get('price'))}) -> {end_swing.get('type')} ({format_price_standard(end_swing.get('price'))}). Sonraki: {last_swing_type}")

        return start_swing, end_swing, last_swing_type


    def calculate_fibonacci_levels(self, swing_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Verilen swing noktalarına göre Fibonacci seviyelerini hesaplar.

        Args:
            swing_points: PivotAnalyzer'dan gelen swing noktalarının listesi.

        Returns:
            Hesaplanmış Fibonacci seviyelerini içeren bir sözlük veya None.
            Sözlük yapısı:
            {
                "start_swing": {...},
                "end_swing": {...},
                "is_uptrend": bool,
                "last_swing_type": str,
                "levels": { ratio: {"price": float, "name": str}, ... },
                "zones": { "zone_name": {"start": float, "end": float, "name": str}, ... }
            }
        """
        fib_points_data = self._get_fib_points(swing_points)
        if not fib_points_data:
            return None

        start_swing, end_swing, last_swing_type = fib_points_data
        start_price = start_swing['price']
        end_price = end_swing['price']

        # ÖNEMLİ DEĞİŞİKLİK: Fibonacci seviyelerini hesaplarken
        # her zaman yüksek fiyat 0.0, düşük fiyat 1.0 olacak şekilde ayarla
        high_price = max(start_price, end_price)
        low_price = min(start_price, end_price)
        price_diff = high_price - low_price

        if price_diff == 0:
            logger.warning("Fibonacci için seçilen swing noktalarının fiyatları aynı, hesaplama yapılamıyor.")
            return None

        # Fibonacci seviyelerini hesapla
        calculated_levels = {}

        # 0.0 seviyesi her zaman yüksek fiyat (görseldeki gibi)
        calculated_levels[0.0] = high_price
        # 1.0 seviyesi her zaman düşük fiyat (görseldeki gibi)
        calculated_levels[1.0] = low_price

        # Diğer seviyeleri hesapla
        for ratio in self.sorted_ratios:
            if ratio not in [0.0, 1.0]:  # 0 ve 1 zaten ayarlandı
                # Formül: yüksek_fiyat - (fiyat_farkı * oran)
                # Bu formül, 0.0'ın yüksek fiyat, 1.0'ın düşük fiyat olduğu duruma göre ayarlandı
                level_price = high_price - (price_diff * ratio)
                calculated_levels[ratio] = level_price

        # Trend yönünü belirle (çıktı için)
        is_uptrend = end_price > start_price

        # Çıktı yapısını oluştur
        fib_data = {
            "start_swing": start_swing,
            "end_swing": end_swing,
            "is_uptrend": is_uptrend,
            "last_swing_type": last_swing_type,
            "levels": {},
            "zones": {}
        }

        # Hesaplanan seviyeleri ve isimlerini ekle
        for ratio, price in calculated_levels.items():
            name = self.levels_config.get(ratio, f"{ratio:.3f}")
            fib_data["levels"][ratio] = {
                "price": price,
                "name": name
            }

        # Özel bölgeleri tanımla
        def get_level(ratio):
            return fib_data["levels"].get(ratio)

        # Extend Zone 1 (0.114 - 0.214)
        l1, l2 = get_level(0.114), get_level(0.214)
        if l1 and l2:
            fib_data["zones"]["ext_zone1"] = {"start": min(l1['price'], l2['price']), "end": max(l1['price'], l2['price']), "name": "Ext Zone 1"}

        # Golden Spot 1 (0.34 - 0.382)
        l1, l2 = get_level(0.34), get_level(0.382)
        if l1 and l2:
            fib_data["zones"]["golden_spot1"] = {"start": min(l1['price'], l2['price']), "end": max(l1['price'], l2['price']), "name": "Golden Spot"}

        # EQ (0.5) - Zaten levels içinde var

        # Golden Spot 2 (0.618 - 0.66)
        l1, l2 = get_level(0.618), get_level(0.66)
        if l1 and l2:
            fib_data["zones"]["golden_spot2"] = {"start": min(l1['price'], l2['price']), "end": max(l1['price'], l2['price']), "name": "Golden Spot"}

        # OTE (0.705 - 0.786 arası) - Bullish OTE
        l1, l2 = get_level(0.705), get_level(0.786)
        if l1 and l2:
            fib_data["zones"]["ote"] = {"start": min(l1['price'], l2['price']), "end": max(l1['price'], l2['price']), "name": "OTE"}

        # Bearish OTE (0.295 - 0.214 arası) - Bullish OTE'nin karşıtı
        l1, l2 = get_level(0.295), get_level(0.214)
        if l1 and l2:
            fib_data["zones"]["bearish_ote"] = {"start": min(l1['price'], l2['price']), "end": max(l1['price'], l2['price']), "name": "Bearish OTE"}

        # Extend Zone 2 (0.786 - 0.886)
        l1, l2 = get_level(0.786), get_level(0.886)
        if l1 and l2:
            fib_data["zones"]["ext_zone2"] = {"start": min(l1['price'], l2['price']), "end": max(l1['price'], l2['price']), "name": "Ext Zone 2"}

        # Extend Zone 3 (1.114 - 1.214)
        l1, l2 = get_level(1.114), get_level(1.214)
        if l1 and l2:
            fib_data["zones"]["ext_zone3"] = {"start": min(l1['price'], l2['price']), "end": max(l1['price'], l2['price']), "name": "Ext Zone 3"}

        # Negatif Extend Zone (-0.114 - -0.214)
        l1, l2 = get_level(-0.114), get_level(-0.214)
        if l1 and l2:
            fib_data["zones"]["neg_ext_zone"] = {"start": min(l1['price'], l2['price']), "end": max(l1['price'], l2['price']), "name": "Neg Ext Zone"}

        # Log mesajını "Line Up/Down" olarak güncelle
        line_direction = "Line Up" if is_uptrend else "Line Down"
        # Konsol loglarını azaltmak için info -> debug seviyesine düşürüldü
        logger.debug(f"Fibonacci seviyeleri hesaplandı. Yön: {line_direction}. Son Swing: {last_swing_type}. Start: {format_price_standard(start_price)}, End: {format_price_standard(end_price)}")
        logger.debug(f"Fibonacci hesaplaması: Yüksek fiyat (0.0): {format_price_standard(high_price)}, Düşük fiyat (1.0): {format_price_standard(low_price)}")
        return fib_data

    def calculate_recent_swing_range(self, swing_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        En son oluşan swing high ve swing low arasındaki aralığı ve
        denge (Equilibrium - EQ) seviyesini hesaplar.

        Args:
            swing_points: PivotAnalyzer'dan gelen swing noktalarının listesi.

        Returns:
            Hesaplanmış aralık ve EQ bilgilerini içeren bir sözlük veya None.
            Sözlük yapısı:
            {
                "high_swing": {...},
                "low_swing": {...},
                "high_price": float,
                "low_price": float,
                "equilibrium_price": float
            }
        """
        # Yalnızca tipi olan geçerli swing noktalarını filtrele ve sırala
        valid_swings = [sp for sp in swing_points if sp.get('type')]
        valid_swings.sort(key=lambda x: x['index']) # Zaman sırasına göre sırala

        if len(valid_swings) < 2:
            logger.debug("Son swing aralığı için yeterli swing noktası bulunamadı (en az 2 gerekli).")
            return None

        # En son iki swing noktasını al
        last_swing = valid_swings[-1]
        prev_swing = valid_swings[-2]

        # Bu iki swing'in tipleri farklı olmalı (biri H, biri L)
        if last_swing['type'] == prev_swing['type']:
            logger.debug(f"Son iki swing aynı tipte ({last_swing['type']}), aralık hesaplanamıyor.")
            # Daha geriye giderek farklı tipli bir önceki swing'i bulmayı deneyebiliriz (opsiyonel)
            if len(valid_swings) >= 3:
                 prev_swing = valid_swings[-3]
                 if last_swing['type'] == prev_swing['type']:
                     logger.warning(f"Son üç swing'den ikisi aynı tipte ({last_swing['type']}), aralık hesaplanamıyor.")
                     return None # Yine aynıysa hesaplama yapma
            else:
                 return None # Yeterli swing yok

        # Hangisi high, hangisi low belirle
        if last_swing['price'] > prev_swing['price']:
            high_swing = last_swing
            low_swing = prev_swing
        else:
            high_swing = prev_swing
            low_swing = last_swing

        high_price = high_swing['price']
        low_price = low_swing['price']

        if high_price == low_price:
            logger.warning("Son swing high ve low fiyatları aynı, EQ hesaplanamıyor.")
            return None

        # Equilibrium hesapla
        equilibrium_price = low_price + (high_price - low_price) * 0.5

        logger.debug(f"Son Swing Aralığı Hesaplandı: Low={format_price_standard(low_price)} ({low_swing['type']}@{low_swing['timestamp']}), High={format_price_standard(high_price)} ({high_swing['type']}@{high_swing['timestamp']}), EQ={format_price_standard(equilibrium_price)}")

        return {
            "high_swing": high_swing,
            "low_swing": low_swing,
            "high_price": high_price,
            "low_price": low_price,
            "equilibrium_price": equilibrium_price
        }