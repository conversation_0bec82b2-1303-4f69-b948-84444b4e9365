# Automaton Puanlama Sistemi Dokü<PERSON>u (v1.7 - Kalıcı Raporlar)

**<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (v1.7):**
- Rap<PERSON><PERSON>a sistemi güncellendi:
  - Bir rapor gönderildikten sonra, sembolün pattern koşulu değişene kadar (yok olana veya yeni pivot oluşana kadar) rapor Telegram'da kalacak
  - Her döngüde puanlar yeniden hesaplanmayacak, sadece pattern koşulu değiştiğinde yeniden hesaplanacak
  - <PERSON><PERSON> <PERSON><PERSON>, raporların daha kalıcı olmasını ve her döngüde değişmemesini sağlayacak

**<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (v1.6):**
- Puanlama sistemine minimum puan eşiği (threshold) eklendi:
  - Net puanı 2.0'ın altında olan semboller raporlanmayacak
  - <PERSON><PERSON><PERSON><PERSON>, d<PERSON>ş<PERSON>k puanlı sinyalleri filtreleyerek daha gü<PERSON>l<PERSON> sinyallere odaklanmayı sağlar

**<PERSON><PERSON><PERSON>ş<PERSON><PERSON><PERSON> (v1.5):**
- Fibonacci bölgelerinin puanlamasına yön bazlı koşullar eklendi:
  - Extend Zone 1 (0.114-0.214) ve Negatif Extend Zone (-0.114--0.214) sadece bearish patternlerde puan alır
  - Extend Zone 2 (0.786-0.886) ve Extend Zone 3 (1.114-1.214) sadece bullish patternlerde puan alır
  - Golden Spot 1, Golden Spot 2 ve OTE bölgeleri hem bullish hem bearish patternlerde puan almaya devam eder

**Değişiklikler (v1.4):**
- Fibonacci seviyelerine negatif değerler (-0.618, -0.382, -0.214, -0.114) eklendi
- Fibonacci seviyelerine 1.0'ın üzerindeki değerler (1.114, 1.214, 1.618) eklendi
- OTE bölgesi tek bir seviye (0.705) yerine bir aralık (0.705-0.786) olarak tanımlandı
- Fibonacci puanlama sistemi yeniden düzenlendi:
  - Golden Spot 1 (0.34-0.382), Golden Spot 2 (0.618-0.66) ve OTE (0.705-0.786) bölge olarak korundu
  - Yeni bölgeler eklendi: Extend Zone 1 (0.114-0.214), Extend Zone 2 (0.786-0.886), Extend Zone 3 (1.114-1.214), Negatif Extend Zone (-0.114--0.214)
  - Diğer tüm seviyeler (EQ dahil) seviye olarak sınıflandırıldı
  - Bölgeler 1.5 puan, seviyeler 1.0 puan alacak şekilde düzenlendi
  - Bölgelere denk gelen seviyeler puanlamadan çıkarıldı

Bu doküman, Automaton projesindeki teknik analiz sonuçlarını birleştirerek işlem fırsatlarını değerlendiren ve puanlayan `ScoringSystem` sınıfının çalışma mantığını açıklar. Sistem, "Confluence" (Kesişim) stratejisine dayanır; yani birden fazla gösterge veya formasyon aynı yönde sinyal verdiğinde daha yüksek puan atanır.

## Genel Akış

1.  **Veri Toplama (`main.py`)**: Belirli bir sembol için tanımlı *tüm* zaman dilimlerindeki (örn: D, 12h, 4s, 1h) analiz verileri (`analyze_symbol` ile) toplanır.
2.  **Veri Yapılandırma (`all_timeframe_data`)**: Toplanan bu veriler, `main.py` içinde `all_timeframe_data` sözlüğünde yapılandırılır.
3.  **Puanlama Sistemi (`ScoringSystem`)**: `calculate_score` metodu, ana zaman dilimi verileri ve `all_timeframe_data` sözlüğü ile çağrılır.
4.  **Ana Puanlama (`calculate_score`)**: Ana zaman dilimi (örn: 4s) verileriyle çağrılır.
    *   **Temel Puan (Base Score)**: Sadece ana zaman dilimindeki (4s) formasyonlara göre hesaplanır.
    *   **İşlem Yönü (Trade Direction)**: Ana zaman dilimindeki formasyonların türüne göre (sadece Bull, sadece Bear veya karışık/yok) veya fallback mekanizmaları (Swing Dizisi, SuperTrend, Daily EQ, Daily Bias, EMA50) ile belirlenir. # fallback kaldırıldı
    *   **Ana Zaman Dilimi Teyit/Negatif Skorları (`_calculate_main_confirmation_scores`)**: Ana zaman dilimindeki göstergeler (EMA, VWAP, Funding, Daily Bias, SuperTrend, Divergence, SFP, **FVG, OB, Fibonacci, EQ Seviyeleri, BOS vb.**) *işlem yönü* ile uyumluluklarına göre Teyit veya Negatif Puan'a eklenir.
    *   **Diğer Zaman Dilimi Skorları (`_calculate_other_timeframe_scores`)**: `all_timeframe_data` aracılığıyla diğer zaman dilimlerinin (şu an sadece 4H için kodlanmış olsa da D, 12h, 1h hedeflenir) verilerine erişilir. Belirlenen *işlem yönü* ile uyumlu sinyaller (örn: **4H FVG, 4H OB**, 4H Swing Range) Teyit Puanı'na, uyumsuz sinyaller Negatif Puan'a %50 ağırlıkla eklenir. **Not: OB'ler için sadece içerideki etkileşim, FVG'ler için içerideki ve %1 yakınlıktaki dış etkileşim puanlanır.**
    *   **Net Puan (Net Score)**: `Net Puan = Temel Puan + Teyit Puanı + Negatif Puan` formülüyle hesaplanır. (Negatif Puan zaten eksili bir değerdir).
5.  **Raporlama (`generate_score_report`)**: Belirlenen minimum net puan eşiğini (2.0) geçen semboller için Telegram raporu oluşturulur.
6.  **İstatistik Takibi (`StatsTracker`)**: Eşiği geçen ve raporlanan sinyaller, `StatsTracker` tarafından kaydedilir ve takibi yapılır.

## Puanlama Bileşenleri

### 1. Temel Puan (Base Score)

*   **Kaynak**: Sadece **ana zaman dilimi (4s)** formasyonları (`patterns`).
*   **Mantık**:
    *   Her bir 4s formasyonu için puan verilir.
    *   "BOS" içeren formasyonlar daha yüksek puan alır.
*   **Puanlama**:
    *   Normal 4s Formasyon: `****` puan
    *   "BOS" içeren 4s Formasyon: `****` puan
*   **Not**: Bu puan, işlem yönünden bağımsız olarak sadece formasyon varlığına göre verilir.

### 2. Teyit Puanı (Confirmation Score)

Bu puan, belirlenen **işlem yönünü** destekleyen sinyallerden gelir.

*   **Diğer Zaman Dilimi Formasyonları (`_score_patterns_other_tf`)**:
    *   Kaynak: D, 12h, 1h `patterns`
    *   Koşul: Formasyon tipi (Bullish/Bearish) işlem yönü ile aynı ise.
    *   Puan (Ana Puanlamada Eklenir):
        *   D: `+1.0`
        *   12h: `+0.75`
        *   1h: `+0.5` # Formasyonlarda 1h kullanılmıyor.
    *   Puan (`_calculate_other_timeframe_scores` içinde %50 ağırlıkla eklenir): Yukarıdaki puanların yarısı.
*   **BOS (Break of Structure) (`_score_bos_other_tf`)**:
    *   Kaynak: D, 12h, 1h, **ve 4s** `bos_results` (Ana TF için `_calculate_main...` içinde çağrılır).
    *   Koşul: BOS yönü (Bullish/Bearish) işlem yönü ile aynı ise.
    *   Puan:
        *   D: `+2.0`
        *   12h: `+1.5`
        *   4s: `+1.5` (BOS puanı diğer zaman dilimlerine göre ayarlanabilir, şimdilik 12h ile aynı varsayıldı)
        *   1h: `+1.0`
    *   Puan (`_calculate_other_timeframe_scores` içinde %50 ağırlıkla eklenir): Yukarıdaki puanların yarısı (eğer ilgili TF orada işlenirse).
*   **EMA (Ana Zaman Dilimi - `_score_ema`)**:
    *   Kaynak: 4s `stats` (EMA 26, 50, 100)
    *   Koşul: Fiyat ilgili EMA'nın üzerindeyse ve yön "bull" ise VEYA fiyat EMA'nın altındaysa ve yön "bear" ise.
    *   Puan: Her uyumlu EMA için `+0.5`
*   **VWAP (Ana Zaman Dilimi - `_score_vwap`)**:
    *   Kaynak: 4s `stats`
    *   Koşul: Fiyat VWAP üzerindeyse ve yön "bull" ise VEYA fiyat VWAP altındaysa ve yön "bear" ise.
    *   Puan: `+1.0`
*   **Funding Rate (Ana Zaman Dilimi - `_score_funding_rate`)**:
    *   Kaynak: 4s `stats`
    *   Koşul: Funding Rate negatif ve yön "bull" ise VEYA Funding Rate pozitif ve yön "bear" ise.
    *   Puan: `+0.5`
*   **Daily Bias (Ana Zaman Dilimi - `_score_daily_bias`)**:
    *   Kaynak: D `daily_bias` (diğer TF'lere kopyalanır)
    *   Koşul: Daily Bias (1 veya -1) işlem yönü ile aynı ise.
    *   Puan: `+0.5` #işlem yönünün tersine negatif puanlama yapılabilir.
*   **SuperTrend (Ana Zaman Dilimi - `_score_supertrend`)**:
    *   Kaynak: 4s `supertrend`
    *   Koşul: SuperTrend yönü (up/down) işlem yönü ile aynı ise.
    *   Puan: `+0.5`
    *   Ek Koşul: Fiyata yakınsa (`distance_percent < 2.0`) ekstra `+0.5` puan.
    *   Ek Koşul: Yeni bir SuperTrend sinyali oluşmuşsa (`signal` != "neutral") ekstra `+0.5` puan.
*   **Divergence (Tüm Zaman Dilimleri - `_score_divergence`)**:
    *   Kaynak: D, 12h, 4s, 1h `divergences` (İlgili hesaplama fonksiyonunda çağrılır).
    *   Koşul: Divergence tipi (Positive/Bullish veya Negative/Bearish) işlem yönü ile aynı ise.
    *   Puan: Her uyumlu divergence için `+0.5` (Gizli divergence'lar da dahil).
*   **SFP (Tüm Zaman Dilimleri - `_score_sfp`)**:
    *   Kaynak: D, 12h, 4s, 1h `sfps` (İlgili hesaplama fonksiyonunda çağrılır).
    *   Koşul: En son oluşan SFP'nin tipi (Bullish/Bearish) işlem yönü ile aynı ise.
    *   Puan: `+1.0`
*   **Naked POC (Ana Zaman Dilimi - `_score_naked_poc`)**:
    *   Kaynak: Ana TF `naked_pocs` ve `stats`.
    *   Koşul: Fiyat, ana zaman dilimindeki bir Naked POC seviyesine yakınsa (%2 tolerans). Yönden bağımsız.
    *   Puan: `+1.0` (En yakın kesişen POC için).
*   **Order Blocks (`_score_order_blocks`)**:
    *   Kaynak: İlgili TF (örn. 4s, 12h) `order_blocks` ve `stats`.
    *   Koşul: Fiyat **OB sınırları içindeyse**.
    *   Puan:
        *   Yön "bull" ve OB "Bullish" ise: `+1.0`
        *   Yön "bear" ve OB "Bearish" ise: `+1.0`
        *   (Diğer durumlar Negatif Puana gider)
    *   Puan (`_calculate_other_timeframe_scores` içinde %50 ağırlıkla eklenir): Yukarıdaki puanların yarısı.
*   **Fibonacci Seviyeleri (Ana Zaman Dilimi - `_score_fibonacci`)**:
    *   Kaynak: Ana TF `fib_data`, `stats` ve `trade_direction`.
    *   Puanlama:
        *   **Bölgeler (Zones):**
            *   Fiyat Golden Spot 1 (0.34-0.382) içinde: `+1.5` (Yönden bağımsız)
            *   Fiyat Golden Spot 2 (0.618-0.66) içinde: `+1.5` (Yönden bağımsız)
            *   Fiyat OTE (0.705-0.786) içinde: `+1.5` (Yönden bağımsız)
            *   Fiyat Extend Zone 1 (0.114-0.214) içinde: `+1.5` (Sadece bearish patternlerde)
            *   Fiyat Extend Zone 2 (0.786-0.886) içinde: `+1.5` (Sadece bullish patternlerde)
            *   Fiyat Extend Zone 3 (1.114-1.214) içinde: `+1.5` (Sadece bullish patternlerde)
            *   Fiyat Negatif Extend Zone (-0.114--0.214) içinde: `+1.5` (Sadece bearish patternlerde)
        *   **Seviyeler (Levels):**
            *   Fiyat herhangi bir Fibonacci seviyesinde (%0.5 tolerans): `+1.0`
                *   Desteklenen seviyeler: -1.618, -1.214, -1.114, -1.0, -0.886, -0.786, -0.705, -0.66, -0.618, -0.5, -0.382, -0.34, 0.0, 0.5 (EQ), 1.0, 1.618
                *   Not: Bölgelere denk gelen seviyeler (0.114, 0.214, 0.34, 0.382, 0.618, 0.66, 0.705, 0.786, 0.886, 1.114, 1.214, -0.114, -0.214) puanlamadan çıkarıldı
        *   **Ek Puanlama:**
            *   Yükselen Fib + Son Swing Low (LL/HL): `+1.0`
            *   Düşen Fib + Son Swing High (HH/LH): `+1.0`
*   **FVG (Fair Value Gap) (`_score_fvg`)**:
    *   Kaynak: İlgili TF (örn. 4s, D) `fvgs` ve `stats`.
    *   Koşul 1: Fiyat FVG içindeyse.
    *   Puan 1: `+1.0` (Doldurulmuşsa `+0.5`).
    *   Koşul 2: Fiyat FVG EQ (denge) noktasına yakınsa (%5 bant).
    *   Puan 2: `+1.5` (Doldurulmuşsa `+0.75`).
    *   Koşul 3 (Sadece 4s ve diğer non-Daily TF'ler): Fiyat FVG'nin dışındaysa ve sınıra %1'den yakınsa:
        *   Yön "bull" ve fiyat **altında** (Discount): `+1.0`
        *   Yön "bear" ve fiyat **üstünde** (Premium): `+1.0`
        *   (Diğer durumlar Negatif Puana gider)
    *   Puan (`_calculate_other_timeframe_scores` içinde %50 ağırlıkla eklenir): Yukarıdaki puanların yarısı.
*   **EQ Seviyeleri (Ana Zaman Dilimi - `_score_eq_levels`)**:
    *   Kaynak: Ana TF `timeframe_levels` ve `stats`.
    *   Koşul: Fiyat ilgili EQ seviyesinin (EQD, EQW, EQM, MREQ) üzerindeyse ve yön "bull" ise VEYA fiyat EQ seviyesinin altındaysa ve yön "bear" ise.
    *   Puan: Her uyumlu EQ seviyesi için `+0.5`.

### 3. Negatif Puan (Negative Score)

Bu puan, belirlenen **işlem yönüyle çelişen** sinyallerden gelir.

*   **EMA (Ana Zaman Dilimi - `_score_ema`)**:
    *   Koşul: Fiyat ilgili EMA'nın altındaysa ve yön "bull" ise VEYA fiyat EMA'nın üzerindeyse ve yön "bear" ise.
    *   Puan: Her uyumsuz EMA için `-0.5`
*   **VWAP (Ana Zaman Dilimi - `_score_vwap`)**:
    *   Koşul: Fiyat VWAP altındaysa ve yön "bull" ise VEYA fiyat VWAP üzerindeyse ve yön "bear" ise.
    *   Puan: `-0.5`
*   **Funding Rate (Ana Zaman Dilimi - `_score_funding_rate`)**:
    *   Koşul: Yön "bull" iken Funding Rate > 0.005 ise VEYA yön "bear" iken Funding Rate < -0.005 ise.
    *   Puan: `-0.5` ile `-2.0` arasında dinamik (Funding Rate büyüklüğüne bağlı).
*   **Daily Bias (Ana Zaman Dilimi - `_score_daily_bias`)**:
    *   Koşul: Daily Bias (1 veya -1) işlem yönü ile zıt ise.
    *   Puan: `-0.5`
*   **SuperTrend (Ana Zaman Dilimi - `_score_supertrend`)**:
    *   Koşul: SuperTrend yönü (up/down) işlem yönü ile zıt ise.
    *   Puan: `-0.5`
*   **Divergence (Tüm Zaman Dilimleri - `_score_divergence`)**:
    *   Koşul: Divergence tipi (Positive/Bullish veya Negative/Bearish) işlem yönü ile zıt ise.
    *   Puan: Her uyumsuz divergence için `-0.5`.
*   **SFP (Tüm Zaman Dilimleri - `_score_sfp`)**:
    *   Koşul: İşlem yönü "bull" iken Bearish SFP varsa VEYA işlem yönü "bear" iken Bullish SFP varsa.
    *   Puan: `-1.0` (Tespit edilen zıt yönlü SFP sayısına göre artabilir).
*   **Order Blocks (`_score_order_blocks`)**:
    *   Kaynak: İlgili TF (örn. 4s, 12h) `order_blocks` ve `stats`.
    *   Koşul: Fiyat **OB sınırları içindeyse**.
    *   Puan:
        *   Yön "bull" ve OB "Bearish" ise: `-1.0`
        *   Yön "bear" ve OB "Bullish" ise: `-1.0`
    *   Puan (`_calculate_other_timeframe_scores` içinde %50 ağırlıkla eklenir): Yukarıdaki puanların yarısı.
*   **FVG (Fair Value Gap) (`_score_fvg`)**:
    *   Kaynak: İlgili TF (örn. 4s, D) `fvgs` ve `stats`.
    *   Koşul (Sadece 4s ve diğer non-Daily TF'ler): Fiyat FVG'nin dışındaysa ve sınıra %1'den yakınsa:
        *   Yön "bull" ve fiyat **üstünde** (Premium): `-1.0`
        *   Yön "bear" ve fiyat **altında** (Discount): `-1.0`
    *   Puan (`_calculate_other_timeframe_scores` içinde %50 ağırlıkla eklenir): Yukarıdaki puanların yarısı.
*   **EQ Seviyeleri (Ana Zaman Dilimi - `_score_eq_levels`)**:
    *   Koşul: Fiyat ilgili EQ seviyesinin (EQD, EQW, EQM, MREQ) altındaysa ve yön "bull" ise VEYA fiyat EQ seviyesinin üzerindeyse ve yön "bear" ise.
    *   Puan: Her uyumsuz EQ seviyesi için `-0.5`.

### 4. Net Puan (Net Score)

*   **Hesaplama**: `Net Puan = Temel Puan + Teyit Puanı + Negatif Puan`
*   **Kullanım**: Bu nihai skor, işlem fırsatının gücünü temsil eder ve sinyal raporlaması ile istatistik takibi için kullanılır.

## İşlem Seviyeleri Hesaplama (`_calculate_trade_levels`)

*   Ana zaman diliminde (4s) net bir işlem yönü (`trade_direction`) belirlendiğinde:
    *   **Giriş (Entry)**: Mevcut son fiyat (`stats['last_price']`).
    *   **Stop Loss (SL)**:
        *   Bull için: Giriş fiyatının %2.3 altı.
        *   Bear için: Giriş fiyatının %2.3 üstü.
    *   **Take Profit (TP)**: Stop loss mesafesine göre 1.5 R:R (Risk/Reward) oranı hedeflenerek hesaplanır.
        *   Bull için: `Giriş + (SL Mesafesi * 1.5)`
        *   Bear için: `Giriş - (SL Mesafesi * 1.5)`
*   Bu hesaplanan seviyeler, `scoring_system.scores` içinde saklanır ve raporlamada kullanılır.

## Önemli Notlar

*   Puan değerleri ve koşullar zamanla optimize edilebilir. Bu doküman, mevcut koda (`scoring_system.py`) göre en güncel hali yansıtmaktadır.
*   Bazı puanlama bileşenleri (Fibonacci, FVG, Order Blocks vb.) varsayılan olarak belirli zaman dilimlerinde (örn. D, 4s) hesaplanır ve puanlamada kullanılır.
*   `all_timeframe_data` yapısı, puanlama sisteminin farklı zaman dilimlerindeki verilere tutarlı bir şekilde erişmesini sağlar.